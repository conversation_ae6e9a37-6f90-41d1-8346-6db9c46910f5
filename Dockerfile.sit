FROM golang:1.24.2 AS builder
WORKDIR /app
COPY go.mod go.sum ./
COPY backend-common-lib/ ./backend-common-lib/

COPY . .
RUN go mod tidy && go clean -modcache
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o main ./cmd

FROM alpine:latest
ARG ENV_FILE
RUN apk add --no-cache ca-certificates tzdata
ENV TZ=Asia/Bangkok
WORKDIR /app
COPY --from=builder /app/main .

# Copy configuration files
COPY ./conf ./conf

# Create cert directory for certificate files
RUN mkdir -p /app/cert

# Copy and setup entrypoint script
COPY ./entrypoint.sh /app/entrypoint.sh
RUN chmod +x /app/entrypoint.sh

COPY ${ENV_FILE} .env

EXPOSE 8184
ENTRYPOINT ["/app/entrypoint.sh"]
