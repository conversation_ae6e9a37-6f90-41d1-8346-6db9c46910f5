package service

import (
	"fmt"
	"net/http"
	"time"

	"backend-common-lib/constant"
	"backend-common-lib/erp"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/global"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	repository "content-service/internal/repository/master_seller_offer"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func (s *masterSellerOfferService) SearchMasterSellerOfferFilter(req dto.MasterSellerOfferPageReqDto) (dto.MasterSellerOfferPageRespDto[dto.MasterSellerOfferDto], error) {
	resp := dto.MasterSellerOfferPageRespDto[dto.MasterSellerOfferDto]{}
	result, err := s.Repo.FindMasterSellerOfferWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Count
	count, err := s.Repo.CountMasterSellerOfferWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.MasterSellerOfferDto, len(result))
	for i, v := range result {
		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.MasterSellerOfferDto](v)
	}

	//NOTE Get latest sync date
	var latestSyncDate *time.Time
	if len(result) > 0 {
		latestSyncDate = result[0].LatestSyncDate
	} else {
		latestSyncDate, err = s.Repo.FindMasterSellerOfferLatestSyncDate()
		if err != nil {
			return resp, errs.NewError(http.StatusInternalServerError, err)
		}
	}

	//NOTE - Response
	resp = dto.MasterSellerOfferPageRespDto[dto.MasterSellerOfferDto]{
		PagingModel:    *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
		LatestSyncDate: latestSyncDate,
	}

	return resp, nil
}

func (s *masterSellerOfferService) UpdateMasterSellerOfferStatus(req dto.MasterSellerOfferUpdateReqDto) error {
	fieldsToUpdate := map[string]interface{}{
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": util.Now(),
	}

	affectedRows, err := s.Repo.UpdatesMasterSellerOfferFieldsWhere(fieldsToUpdate, "id = ?", req.Id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(fiber.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}

	return nil
}

func (s *masterSellerOfferService) SyncMasterSellerOfferFromErp(actionBy *int, erpConfig global.ErpConfig, authConfig global.AuthConfig) error {
	erpMap, allKeys, err := s.getSellerOfferFromErp(erpConfig, authConfig)
	if err != nil {
		return err
	}
	dbMap, err := s.getSellerOfferFromDb(allKeys)
	if err != nil {
		return err
	}
	err = s.syncSellerOffer(actionBy, erpMap, dbMap, allKeys)
	if err != nil {
		return err
	}

	return nil
}

func (s *masterSellerOfferService) getSellerOfferFromErp(erpConfig global.ErpConfig, authConfig global.AuthConfig) (map[string]dto.MasterSellerOfferDto, map[string]struct{}, error) {
	erpList, err := erp.FetchListFromErp[dto.MasterSellerOfferSyncErpRespDto](
		erpConfig.SellerOfferUrl,
		authConfig.LoginUrl,
		authConfig.Email,
		authConfig.Password,
		map[string]interface{}{"Company_Name": "AUCT"},
		"data",
	)

	if err != nil {
		return nil, nil, errs.NewError(http.StatusInternalServerError, err)
	}

	erpMap := make(map[string]dto.MasterSellerOfferDto)
	allKeys := make(map[string]struct{})

	for _, e := range erpList {

		erpMap[util.Val(e.SellerOfferCode)] = dto.MasterSellerOfferDto{
			CompanyCode:     e.CompanyCode,
			SellerOfferCode: e.SellerOfferCode,
			DescriptionTh:   e.DescriptionTh,
			DescriptionEn:   e.DescriptionEn,
			Split:           e.Split,
			Invoice1:        e.Invoice1,
			Invoice2:        e.Invoice2,
			VendorCode:      e.VendorCode,
			IsActive:        e.Status == "Active",
			IsDeletedByErp:  false,
		}
		allKeys[util.Val(e.SellerOfferCode)] = struct{}{}
	}

	return erpMap, allKeys, nil
}

func (s *masterSellerOfferService) getSellerOfferFromDb(allKeys map[string]struct{}) (map[string]entity.MasterSellerOffer, error) {
	dbList, err := s.Repo.FindMasterSellerOfferAll()
	if err != nil {
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	dbMap := make(map[string]entity.MasterSellerOffer)
	for _, e := range dbList {
		dbMap[util.Val(e.SellerOfferCode)] = e
		allKeys[util.Val(e.SellerOfferCode)] = struct{}{}
	}
	return dbMap, nil
}

func (s *masterSellerOfferService) syncSellerOffer(actionBy *int, erpMap map[string]dto.MasterSellerOfferDto, dbMap map[string]entity.MasterSellerOffer, allKeys map[string]struct{}) error {
	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		repo := repository.NewMasterSellerOfferRepository(tx)
		var toInsert []entity.MasterSellerOffer
		currentDateTime := util.NowPtr()

		for code := range allKeys {
			erp, erpExists := erpMap[code]
			db, dbExists := dbMap[code]

			if erpExists && dbExists {
				// มีทั้ง ERP + DB → compare → update หมด
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.CompanyCode = erp.CompanyCode
				temp.SellerOfferCode = erp.SellerOfferCode
				temp.DescriptionTh = erp.DescriptionTh
				temp.DescriptionEn = erp.DescriptionEn
				temp.Split = erp.Split
				temp.VendorCode = erp.VendorCode
				temp.Invoice1 = erp.Invoice1
				temp.Invoice2 = erp.Invoice2
				temp.IsActive = erp.IsActive
				temp.IsDeletedByErp = false
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterSellerOfferAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}

			} else if erpExists && !dbExists {
				// มีใน ERP แต่ไม่มีใน DB → insert
				temp := entity.MasterSellerOffer{
					BaseEntity: &model.BaseEntity{
						CreatedDate: *currentDateTime,
						CreatedBy:   actionBy,
						UpdatedDate: currentDateTime,
						UpdatedBy:   actionBy,
					},
					CompanyCode:     erp.CompanyCode,
					SellerOfferCode: erp.SellerOfferCode,
					DescriptionTh:   erp.DescriptionTh,
					DescriptionEn:   erp.DescriptionEn,
					Split:           erp.Split,
					VendorCode:      erp.VendorCode,
					Invoice1:        erp.Invoice1,
					Invoice2:        erp.Invoice2,
					IsActive:        erp.IsActive,
					IsDeletedByErp:  false,
					LatestSyncDate:  currentDateTime,
				}
				toInsert = append(toInsert, temp)

			} else if !erpExists && dbExists {
				// มีใน DB แต่ไม่มีใน ERP → mark delete
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.IsActive = false
				temp.IsDeletedByErp = true
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterSellerOfferAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}
			}
		}

		if len(toInsert) > 0 {
			if err := repo.InsertMasterSellerOfferList(toInsert); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}
		return nil
	})
}
