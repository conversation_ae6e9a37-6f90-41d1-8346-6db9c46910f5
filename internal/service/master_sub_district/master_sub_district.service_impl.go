package service

import (
	"backend-common-lib/erp"
	"content-service/internal/global"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	repository "content-service/internal/repository/master_sub_district"
	"fmt"
	"net/http"

	"time"

	"backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func (s *masterSubDistrictService) SearchMasterSubDistrictFilter(req dto.MasterSubDistrictPageReqDto) (dto.MasterSubDistrictPageRespDto[dto.MasterSubDistrictDto], error) {
	resp := dto.MasterSubDistrictPageRespDto[dto.MasterSubDistrictDto]{}
	result, err := s.Repo.FindMasterSubDistrictWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Count
	count, err := s.Repo.CountMasterSubDistrictWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.MasterSubDistrictDto, len(result))
	for i, v := range result {
		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.MasterSubDistrictDto](v)
	}

	//NOTE Get latest sync date
	var latestSyncDate *time.Time
	if len(result) > 0 {
		latestSyncDate = result[0].LatestSyncDate
	} else {
		latestSyncDate, err = s.Repo.FindMasterSubDistrictLatestSyncDate()
		if err != nil {
			return resp, errs.NewError(http.StatusInternalServerError, err)
		}
	}

	//NOTE - Response
	resp = dto.MasterSubDistrictPageRespDto[dto.MasterSubDistrictDto]{
		PagingModel:    *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
		LatestSyncDate: latestSyncDate,
	}

	return resp, nil
}

func (s *masterSubDistrictService) UpdateMasterSubDistrictStatus(req dto.MasterSubDistrictUpdateReqDto) error {
	fieldsToUpdate := map[string]interface{}{
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": util.Now(),
	}

	affectedRows, err := s.Repo.UpdatesMasterSubDistrictFieldsWhere(fieldsToUpdate, "id = ?", req.Id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(fiber.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}

	return nil
}

func (s *masterSubDistrictService) SyncMasterSubDistrictFromErp(actionBy *int, erpConfig global.ErpConfig, authConfig global.AuthConfig) error {
	erpMap, allKeys, err := s.getSubDistrictFromErp(erpConfig, authConfig)
	if err != nil {
		return err
	}
	dbMap, err := s.getSubDistrictFromDb(allKeys)
	if err != nil {
		return err
	}
	err = s.syncSubDistrict(actionBy, erpMap, dbMap, allKeys)
	if err != nil {
		return err
	}

	return nil
}

func (s *masterSubDistrictService) getSubDistrictFromErp(erpConfig global.ErpConfig, authConfig global.AuthConfig) (map[string]dto.MasterSubDistrictDto, map[string]struct{}, error) {
	erpList, err := erp.FetchListFromErpWithLongTimeout[dto.MasterSubDistrictSyncErpRespDto](
		erpConfig.SubDistrictUrl,
		authConfig.LoginUrl,
		authConfig.Email,
		authConfig.Password,
		map[string]interface{}{"Company_Name": "AUCT"},
		"data",
	)
	if err != nil {
		return nil, nil, errs.NewError(http.StatusInternalServerError, err)
	}

	erpMap := make(map[string]dto.MasterSubDistrictDto)
	allKeys := make(map[string]struct{})

	for _, e := range erpList {
		erpMap[util.Val(e.SubDistrictCode)] = dto.MasterSubDistrictDto{
			RegionCode:      e.RegionCode,
			CityCode:        e.CityCode,
			DistrictCode:    e.DistrictCode,
			SubDistrictCode: e.SubDistrictCode,
			DescriptionTh:   e.DescriptionTh,
			DescriptionEn:   e.DescriptionEn,
			IsActive:        e.Status == "Active",
		}
		allKeys[util.Val(e.SubDistrictCode)] = struct{}{}
	}
	return erpMap, allKeys, nil
}

func (s *masterSubDistrictService) getSubDistrictFromDb(allKeys map[string]struct{}) (map[string]entity.MasterSubDistrict, error) {
	dbList, err := s.Repo.FindMasterSubDistrictAll()
	if err != nil {
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	dbMap := make(map[string]entity.MasterSubDistrict)
	for _, e := range dbList {
		dbMap[util.Val(e.SubDistrictCode)] = e
		allKeys[util.Val(e.SubDistrictCode)] = struct{}{}
	}
	return dbMap, nil
}

func (s *masterSubDistrictService) syncSubDistrict(actionBy *int, erpMap map[string]dto.MasterSubDistrictDto, dbMap map[string]entity.MasterSubDistrict, allKeys map[string]struct{}) error {
	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		repo := repository.NewMasterSubDistrictRepository(tx)
		var toInsert []entity.MasterSubDistrict
		currentDateTime := util.NowPtr()

		for code := range allKeys {
			erp, erpExists := erpMap[code]
			db, dbExists := dbMap[code]

			if erpExists && dbExists {
				// มีทั้ง ERP + DB → compare → update หมด
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.RegionCode = erp.RegionCode
				temp.CityCode = erp.CityCode
				temp.DistrictCode = erp.DistrictCode
				temp.SubDistrictCode = erp.SubDistrictCode
				temp.DescriptionTh = erp.DescriptionTh
				temp.DescriptionEn = erp.DescriptionEn
				temp.IsActive = erp.IsActive
				temp.IsDeletedByErp = false
				temp.LatestSyncDate = currentDateTime

				if changes := util.CompareStructDeep(db, temp, ""); len(changes) > 0 {
					if err := repo.UpdateMasterSubDistrictAllFields(&temp); err != nil {
						return errs.NewError(http.StatusInternalServerError, err)
					}
				}

			} else if erpExists && !dbExists {
				// มีใน ERP แต่ไม่มีใน DB → insert
				temp := entity.MasterSubDistrict{
					BaseEntity: &model.BaseEntity{
						CreatedDate: *currentDateTime,
						CreatedBy:   actionBy,
						UpdatedDate: currentDateTime,
						UpdatedBy:   actionBy,
					},
					RegionCode:      erp.RegionCode,
					CityCode:        erp.CityCode,
					DistrictCode:    erp.DistrictCode,
					SubDistrictCode: erp.SubDistrictCode,
					DescriptionTh:   erp.DescriptionTh,
					DescriptionEn:   erp.DescriptionEn,
					IsActive:        erp.IsActive,
					IsDeletedByErp:  false,
					LatestSyncDate:  currentDateTime,
				}
				toInsert = append(toInsert, temp)

			} else if !erpExists && dbExists {
				// มีใน DB แต่ไม่มีใน ERP → mark delete
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.IsActive = false
				temp.IsDeletedByErp = true
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterSubDistrictAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}
			}
		}

		if len(toInsert) > 0 {
			if err := repo.InsertMasterSubDistrictListWithBatches(toInsert); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}

		_, err := repo.UpdatesMasterSubDistrictFieldsWhere(map[string]interface{}{"latest_sync_date": currentDateTime, "updated_date": currentDateTime, "updated_by": actionBy}, "is_deleted_by_erp = ?", false)
		if err != nil {
			return errs.NewError(http.StatusInternalServerError, err)
		}

		return nil
	})
}
