package service

import (
	"backend-common-lib/constant"
	"backend-common-lib/erp"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/global"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	repository "content-service/internal/repository/master_cost_revenue"
	"fmt"
	"net/http"

	"time"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func (s *masterCostRevenueService) SearchMasterCostRevenueFilter(req dto.MasterCostRevenuePageReqDto) (dto.MasterCostRevenuePageRespDto[dto.MasterCostRevenueDto], error) {
	resp := dto.MasterCostRevenuePageRespDto[dto.MasterCostRevenueDto]{}
	result, err := s.Repo.FindMasterCostRevenueWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Count
	count, err := s.Repo.CountMasterCostRevenueWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.MasterCostRevenueDto, len(result))
	for i, v := range result {
		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.MasterCostRevenueDto](v)
	}

	//NOTE Get latest sync date
	var latestSyncDate *time.Time
	if len(result) > 0 {
		latestSyncDate = result[0].LatestSyncDate
	} else {
		latestSyncDate, err = s.Repo.FindMasterCostRevenueLatestSyncDate()
		if err != nil {
			return resp, errs.NewError(http.StatusInternalServerError, err)
		}
	}

	//NOTE - Response
	resp = dto.MasterCostRevenuePageRespDto[dto.MasterCostRevenueDto]{
		PagingModel:    *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
		LatestSyncDate: latestSyncDate,
	}

	return resp, nil
}

func (s *masterCostRevenueService) UpdateMasterCostRevenueStatus(req dto.MasterCostRevenueUpdateReqDto) error {
	fieldsToUpdate := map[string]interface{}{
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": util.Now(),
	}

	affectedRows, err := s.Repo.UpdatesMasterCostRevenueFieldsWhere(fieldsToUpdate, "id = ?", req.Id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(fiber.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}

	return nil
}

func (s *masterCostRevenueService) SyncMasterCostRevenueFromErp(actionBy *int, erpConfig global.ErpConfig, authConfig global.AuthConfig) error {
	erpMap, allKeys, err := s.getCostRevenueFromErp(erpConfig, authConfig)
	if err != nil {
		return err
	}
	dbMap, err := s.getCostRevenueFromDb(allKeys)
	if err != nil {
		return err
	}
	err = s.syncCostRevenue(actionBy, erpMap, dbMap, allKeys)
	if err != nil {
		return err
	}

	return nil
}

func (s *masterCostRevenueService) getCostRevenueFromErp(erpConfig global.ErpConfig, authConfig global.AuthConfig) (map[string]dto.MasterCostRevenueDto, map[string]struct{}, error) {
	erpList, err := erp.FetchListFromErp[dto.MasterCostRevenueSyncErpRespDto](
		erpConfig.CostRevenueUrl,
		authConfig.LoginUrl,
		authConfig.Email,
		authConfig.Password,
		map[string]interface{}{"Company_Name": "AUCT"},
		"data",
	)
	if err != nil {
		return nil, nil, errs.NewError(http.StatusInternalServerError, err)
	}

	erpMap := make(map[string]dto.MasterCostRevenueDto)
	allKeys := make(map[string]struct{})

	for _, e := range erpList {
		erpMap[util.Val(e.CostRevenueCode)] = dto.MasterCostRevenueDto{
			CompanyCode:           e.CompanyCode,
			CostRevenueCode:       e.CostRevenueCode,
			DescriptionTh:         e.DescriptionTh,
			DescriptionEn:         e.DescriptionEn,
			ReportingGroup:        e.ReportingGroup,
			IsRefund:              e.IsRefund,
			IsPriceIncludingVat:   e.IsPriceIncludingVat,
			VatCode:               e.VatCode,
			WhtProdCode:           e.WhtProdCode,
			Type:                  e.Type,
			GroupHeader:           e.GroupHeader,
			Wht3Percent:           e.Wht3Percent,
			UnitPrice:             e.UnitPrice != nil && *e.UnitPrice == 1, //NOTE - convert null and 0 1 to bool
			ExcludeSendAr:         e.ExcludeSendAr,
			InterfaceGuid:         e.InterfaceGuid,
			InterfaceStatusCreate: e.InterfaceStatusCreate,
			InterfaceStatusUpdate: e.InterfaceStatusUpdate,
			ApiVatCode:            e.ApiVatCode,
			IsActive:              e.Status == "Active",
		}
		allKeys[util.Val(e.CostRevenueCode)] = struct{}{}
	}
	return erpMap, allKeys, nil
}

func (s *masterCostRevenueService) getCostRevenueFromDb(allKeys map[string]struct{}) (map[string]entity.MasterCostRevenue, error) {
	dbList, err := s.Repo.FindMasterCostRevenueAll()
	if err != nil {
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	dbMap := make(map[string]entity.MasterCostRevenue)
	for _, e := range dbList {
		dbMap[util.Val(e.CostRevenueCode)] = e
		allKeys[util.Val(e.CostRevenueCode)] = struct{}{}
	}
	return dbMap, nil
}

func (s *masterCostRevenueService) syncCostRevenue(actionBy *int, erpMap map[string]dto.MasterCostRevenueDto, dbMap map[string]entity.MasterCostRevenue, allKeys map[string]struct{}) error {
	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		repo := repository.NewMasterCostRevenueRepository(tx)
		var toInsert []entity.MasterCostRevenue
		currentDateTime := util.NowPtr()

		for code := range allKeys {
			erp, erpExists := erpMap[code]
			db, dbExists := dbMap[code]

			if erpExists && dbExists {
				// มีทั้ง ERP + DB → compare → update หมด
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.CompanyCode = erp.CompanyCode
				temp.CostRevenueCode = erp.CostRevenueCode
				temp.DescriptionTh = erp.DescriptionTh
				temp.DescriptionEn = erp.DescriptionEn
				temp.ReportingGroup = erp.ReportingGroup
				temp.IsRefund = erp.IsRefund
				temp.IsPriceIncludingVat = erp.IsPriceIncludingVat
				temp.VatCode = erp.VatCode
				temp.WhtProdCode = erp.WhtProdCode
				temp.Type = erp.Type
				temp.GroupHeader = erp.GroupHeader
				temp.Wht3Percent = erp.Wht3Percent
				temp.UnitPrice = erp.UnitPrice
				temp.ExcludeSendAr = erp.ExcludeSendAr
				temp.InterfaceGuid = erp.InterfaceGuid
				temp.InterfaceStatusCreate = erp.InterfaceStatusCreate
				temp.InterfaceStatusUpdate = erp.InterfaceStatusUpdate
				temp.ApiVatCode = erp.ApiVatCode
				temp.IsActive = erp.IsActive
				temp.IsDeletedByErp = false
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterCostRevenueAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}

			} else if erpExists && !dbExists {
				// มีใน ERP แต่ไม่มีใน DB → insert
				temp := entity.MasterCostRevenue{
					BaseEntity: &model.BaseEntity{
						CreatedDate: *currentDateTime,
						CreatedBy:   actionBy,
						UpdatedDate: currentDateTime,
						UpdatedBy:   actionBy,
					},
					CompanyCode:           erp.CompanyCode,
					CostRevenueCode:       erp.CostRevenueCode,
					DescriptionTh:         erp.DescriptionTh,
					DescriptionEn:         erp.DescriptionEn,
					ReportingGroup:        erp.ReportingGroup,
					IsRefund:              erp.IsRefund,
					IsPriceIncludingVat:   erp.IsPriceIncludingVat,
					VatCode:               erp.VatCode,
					WhtProdCode:           erp.WhtProdCode,
					Type:                  erp.Type,
					GroupHeader:           erp.GroupHeader,
					Wht3Percent:           erp.Wht3Percent,
					UnitPrice:             erp.UnitPrice,
					ExcludeSendAr:         erp.ExcludeSendAr,
					InterfaceGuid:         erp.InterfaceGuid,
					InterfaceStatusCreate: erp.InterfaceStatusCreate,
					InterfaceStatusUpdate: erp.InterfaceStatusUpdate,
					ApiVatCode:            erp.ApiVatCode,
					IsActive:              erp.IsActive,
					IsDeletedByErp:        false,
					LatestSyncDate:        currentDateTime,
				}
				toInsert = append(toInsert, temp)

			} else if !erpExists && dbExists {
				// มีใน DB แต่ไม่มีใน ERP → mark delete
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.IsActive = false
				temp.IsDeletedByErp = true
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterCostRevenueAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}
			}
		}

		if len(toInsert) > 0 {
			if err := repo.InsertMasterCostRevenueList(toInsert); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}
		return nil
	})
}
