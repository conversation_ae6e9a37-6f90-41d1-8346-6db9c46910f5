package service

import (
	"backend-common-lib/constant"
	"backend-common-lib/erp"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/global"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	repository "content-service/internal/repository/master_customer_type"
	"fmt"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func (s *masterCustomerTypeService) SearchMasterCustomerTypeFilter(req dto.MasterCustomerTypePageReqDto) (dto.MasterCustomerTypePageRespDto[dto.MasterCustomerTypeDto], error) {
	resp := dto.MasterCustomerTypePageRespDto[dto.MasterCustomerTypeDto]{}
	result, err := s.Repo.FindMasterCustomerTypeWithFilter(req)
	if err != nil {
		return resp, err
	}

	//NOTE - Count
	count, err := s.Repo.CountMasterCustomerTypeWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.MasterCustomerTypeDto, len(result))
	for i, v := range result {
		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.MasterCustomerTypeDto](v)
	}

	//NOTE Get latest sync date
	var latestSyncDate *time.Time
	if len(result) > 0 {
		latestSyncDate = result[0].LatestSyncDate
	} else {
		latestSyncDate, err = s.Repo.FindMasterCustomerTypeLatestSyncDate()
		if err != nil {
			return resp, err
		}
	}

	//NOTE - Response
	resp = dto.MasterCustomerTypePageRespDto[dto.MasterCustomerTypeDto]{
		PagingModel:    *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
		LatestSyncDate: latestSyncDate,
	}

	return resp, nil
}

func (s *masterCustomerTypeService) UpdateMasterCustomerTypeStatus(req dto.MasterCustomerTypeUpdateReqDto) error {
	fieldsToUpdate := map[string]interface{}{
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": util.Now(),
	}

	affectedRows, err := s.Repo.UpdatesMasterCustomerTypeFieldsWhere(fieldsToUpdate, "id = ?", req.Id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(fiber.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}
	return nil
}

func (s *masterCustomerTypeService) SyncMasterCustomerTypeFromErp(actionBy *int, erpConfig global.ErpConfig, authConfig global.AuthConfig) error {
	erpMap, allKeys, err := s.getCustomerTypeFromErp(erpConfig, authConfig)
	if err != nil {
		return err
	}
	dbMap, err := s.getCustomerTypeFromDb(allKeys)
	if err != nil {
		return err
	}
	err = s.syncCustomerType(actionBy, erpMap, dbMap, allKeys)
	if err != nil {
		return err
	}

	return nil
}

func (s *masterCustomerTypeService) getCustomerTypeFromErp(erpConfig global.ErpConfig, authConfig global.AuthConfig) (map[string]dto.MasterCustomerTypeDto, map[string]struct{}, error) {
	erpList, err := erp.FetchListFromErp[dto.MasterCustomerTypeSyncErpRespDto](
		erpConfig.CustomerTypeUrl,
		authConfig.LoginUrl,
		authConfig.Email,
		authConfig.Password,
		map[string]interface{}{"Company_Name": "AUCT"},
		"data",
	)

	if err != nil {
		return nil, nil, errs.NewError(http.StatusInternalServerError, err)
	}

	erpMap := make(map[string]dto.MasterCustomerTypeDto)
	allKeys := make(map[string]struct{})

	for _, e := range erpList {
		erpMap[util.Val(e.CustomerTypeCode)] = dto.MasterCustomerTypeDto{
			CustomerTypeCode: e.CustomerTypeCode,
			DescriptionTh:    e.DescriptionTh,
			DescriptionEn:    e.DescriptionEn,
			IsWHT:            e.IsWHT,
			IsActive:         e.Status == "Active",
		}
		allKeys[util.Val(e.CustomerTypeCode)] = struct{}{}
	}
	return erpMap, allKeys, nil
}

func (s *masterCustomerTypeService) getCustomerTypeFromDb(allKeys map[string]struct{}) (map[string]entity.MasterCustomerType, error) {
	dbList, err := s.Repo.FindMasterCustomerTypeAll()
	if err != nil {
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	dbMap := make(map[string]entity.MasterCustomerType)
	for _, e := range dbList {
		dbMap[util.Val(e.CustomerTypeCode)] = e
		allKeys[util.Val(e.CustomerTypeCode)] = struct{}{}
	}
	return dbMap, nil
}

func (s *masterCustomerTypeService) syncCustomerType(actionBy *int, erpMap map[string]dto.MasterCustomerTypeDto, dbMap map[string]entity.MasterCustomerType, allKeys map[string]struct{}) error {
	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		repo := repository.NewMasterCustomerTypeRepository(tx)
		var toInsert []entity.MasterCustomerType
		currentDateTime := util.Now()

		for code := range allKeys {
			erp, erpExists := erpMap[code]
			db, dbExists := dbMap[code]

			if erpExists && dbExists {
				// มีทั้ง ERP + DB → compare → update หมด
				temp := db
				temp.UpdatedDate = &currentDateTime
				temp.UpdatedBy = actionBy
				temp.CustomerTypeCode = erp.CustomerTypeCode
				temp.DescriptionTh = erp.DescriptionTh
				temp.DescriptionEn = erp.DescriptionEn
				temp.IsWHT = erp.IsWHT
				temp.IsActive = erp.IsActive
				temp.IsDeletedByErp = false
				temp.LatestSyncDate = &currentDateTime

				if err := repo.UpdateMasterCustomerTypeAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}

			} else if erpExists && !dbExists {
				// มีใน ERP แต่ไม่มีใน DB → insert
				temp := entity.MasterCustomerType{
					BaseEntity: &model.BaseEntity{
						CreatedDate: currentDateTime,
						CreatedBy:   actionBy,
						UpdatedDate: &currentDateTime,
						UpdatedBy:   actionBy,
					},
					CustomerTypeCode: erp.CustomerTypeCode,
					DescriptionTh:    erp.DescriptionTh,
					DescriptionEn:    erp.DescriptionEn,
					IsWHT:            erp.IsWHT,
					IsActive:         erp.IsActive,
				}
				toInsert = append(toInsert, temp)

			} else if !erpExists && dbExists {
				// มีใน DB แต่ไม่มีใน ERP → mark delete
				temp := db
				temp.UpdatedDate = &currentDateTime
				temp.UpdatedBy = actionBy
				temp.IsActive = false
				temp.IsDeletedByErp = true
				temp.LatestSyncDate = &currentDateTime

				if err := repo.UpdateMasterCustomerTypeAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}
			}
		}

		if len(toInsert) > 0 {
			if err := repo.InsertMasterCustomerTypeList(toInsert); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}
		return nil
	})
}
