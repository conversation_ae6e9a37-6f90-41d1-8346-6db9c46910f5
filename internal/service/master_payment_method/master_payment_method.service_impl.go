package service

import (
	"backend-common-lib/constant"
	"backend-common-lib/erp"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/global"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	repository "content-service/internal/repository/master_payment_method"
	"fmt"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func (s *masterPaymentMethodService) SearchMasterPaymentMethodFilter(req dto.MasterPaymentMethodPageReqDto) (dto.MasterPaymentMethodPageRespDto[dto.MasterPaymentMethodDto], error) {
	resp := dto.MasterPaymentMethodPageRespDto[dto.MasterPaymentMethodDto]{}
	result, err := s.Repo.FindMasterPaymentMethodWithFilter(req)
	if err != nil {
		return resp, errs.NewError(fiber.StatusInternalServerError, err)
	}

	//NOTE - Count
	count, err := s.Repo.CountMasterPaymentMethodWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.MasterPaymentMethodDto, len(result))
	for i, v := range result {
		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.MasterPaymentMethodDto](v)
	}

	//NOTE Get latest sync date
	var latestSyncDate *time.Time
	if len(result) > 0 {
		latestSyncDate = result[0].LatestSyncDate
	} else {
		latestSyncDate, err = s.Repo.FindLatestSyncDate()
		if err != nil {
			return resp, errs.NewError(fiber.StatusInternalServerError, err)
		}
	}

	//NOTE - Response
	resp = dto.MasterPaymentMethodPageRespDto[dto.MasterPaymentMethodDto]{
		PagingModel:    *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
		LatestSyncDate: latestSyncDate,
	}

	return resp, nil
}

func (s *masterPaymentMethodService) UpdateMasterPaymentMethodStatus(req dto.MasterPaymentMethodUpdateReqDto) error {
	fieldsToUpdate := map[string]interface{}{
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": util.Now(),
	}

	affectedRows, err := s.Repo.UpdatesMasterPaymentMethodFieldsWhere(fieldsToUpdate, "id = ?", req.Id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(fiber.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}

	return nil
}

func (s *masterPaymentMethodService) SyncMasterPaymentMethodFromErp(actionBy *int, erpConfig global.ErpConfig, authConfig global.AuthConfig) error {
	erpMap, allKeys, err := s.getPaymentMethodFromErp(erpConfig, authConfig)
	if err != nil {
		return err
	}
	dbMap, err := s.getPaymentMethodFromDb(allKeys)
	if err != nil {
		return err
	}
	err = s.syncPaymentMethod(actionBy, erpMap, dbMap, allKeys)
	if err != nil {
		return err
	}

	return nil
}

func (s *masterPaymentMethodService) getPaymentMethodFromErp(erpConfig global.ErpConfig, authConfig global.AuthConfig) (map[string]dto.MasterPaymentMethodDto, map[string]struct{}, error) {
	erpList, err := erp.FetchListFromErp[dto.MasterPaymentMethodSyncErpRespDto](
		erpConfig.PaymentMethodUrl,
		authConfig.LoginUrl,
		authConfig.Email,
		authConfig.Password,
		map[string]interface{}{"Company_Name": "AUCT"},
		"data",
	)
	if err != nil {
		return nil, nil, errs.NewError(http.StatusInternalServerError, err)
	}

	erpMap := make(map[string]dto.MasterPaymentMethodDto)
	allKeys := make(map[string]struct{})

	for _, e := range erpList {
		paymentMethodCode := util.Val(e.PaymentMethodCode)
		erpMap[paymentMethodCode] = dto.MasterPaymentMethodDto{
			CompanyCode:               e.CompanyCode,
			PaymentMethodCode:         e.PaymentMethodCode,
			DescriptionTh:             e.DescriptionTh,
			DescriptionEn:             e.DescriptionEn,
			Type:                      e.Type,
			IsExcludeCalSellerPayment: e.IsExcludeCalSellerPayment,
			IsActive:                  e.Status == "Active",
		}
		allKeys[paymentMethodCode] = struct{}{}
	}
	return erpMap, allKeys, nil
}

func (s *masterPaymentMethodService) getPaymentMethodFromDb(allKeys map[string]struct{}) (map[string]entity.MasterPaymentMethod, error) {
	dbList, err := s.Repo.FindMasterPaymentMethodAll()
	if err != nil {
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	dbMap := make(map[string]entity.MasterPaymentMethod)
	for _, e := range dbList {
		paymentMethodCode := util.Val(e.PaymentMethodCode)
		dbMap[paymentMethodCode] = e
		allKeys[paymentMethodCode] = struct{}{}
	}
	return dbMap, nil
}

func (s *masterPaymentMethodService) syncPaymentMethod(actionBy *int, erpMap map[string]dto.MasterPaymentMethodDto, dbMap map[string]entity.MasterPaymentMethod, allKeys map[string]struct{}) error {
	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		repo := repository.NewMasterPaymentMethodRepository(tx)
		var toInsert []entity.MasterPaymentMethod
		currentDateTime := util.Now()

		for code := range allKeys {
			erp, erpExists := erpMap[code]
			db, dbExists := dbMap[code]

			if erpExists && dbExists {
				// มีทั้ง ERP + DB → compare → update หมด
				temp := db
				temp.UpdatedDate = &currentDateTime
				temp.UpdatedBy = actionBy
				temp.CompanyCode = erp.CompanyCode
				temp.PaymentMethodCode = erp.PaymentMethodCode
				temp.DescriptionTh = erp.DescriptionTh
				temp.DescriptionEn = erp.DescriptionEn
				temp.Type = erp.Type
				temp.IsExcludeCalSellerPayment = erp.IsExcludeCalSellerPayment
				temp.IsActive = erp.IsActive
				temp.IsDeletedByErp = false
				temp.LatestSyncDate = &currentDateTime

				if err := repo.UpdateMasterPaymentMethodAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}

			} else if erpExists && !dbExists {
				// มีใน ERP แต่ไม่มีใน DB → insert
				temp := entity.MasterPaymentMethod{
					BaseEntity: &model.BaseEntity{
						CreatedDate: currentDateTime,
						CreatedBy:   actionBy,
						UpdatedDate: &currentDateTime,
						UpdatedBy:   actionBy,
					},
					CompanyCode:               erp.CompanyCode,
					PaymentMethodCode:         erp.PaymentMethodCode,
					DescriptionTh:             erp.DescriptionTh,
					DescriptionEn:             erp.DescriptionEn,
					Type:                      erp.Type,
					IsExcludeCalSellerPayment: erp.IsExcludeCalSellerPayment,
					IsActive:                  erp.IsActive,
					IsDeletedByErp:            false,
					LatestSyncDate:            &currentDateTime,
				}
				toInsert = append(toInsert, temp)

			} else if !erpExists && dbExists {
				// มีใน DB แต่ไม่มีใน ERP → mark delete
				temp := db
				temp.UpdatedDate = &currentDateTime
				temp.UpdatedBy = actionBy
				temp.IsActive = false
				temp.IsDeletedByErp = true
				temp.LatestSyncDate = &currentDateTime

				if err := repo.UpdateMasterPaymentMethodAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}
			}
		}

		if len(toInsert) > 0 {
			if err := repo.InsertMasterPaymentMethodList(toInsert); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}
		return nil
	})
}
