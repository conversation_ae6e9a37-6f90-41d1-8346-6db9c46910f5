package service

import (
	"backend-common-lib/erp"
	"content-service/internal/global"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	repository "content-service/internal/repository/master_vendor_group"
	"fmt"
	"net/http"

	"time"

	"backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func (s *masterVendorGroupService) SearchMasterVendorGroupFilter(req dto.MasterVendorGroupPageReqDto) (dto.MasterVendorGroupPageRespDto[dto.MasterVendorGroupDto], error) {
	resp := dto.MasterVendorGroupPageRespDto[dto.MasterVendorGroupDto]{}
	result, err := s.Repo.FindMasterVendorGroupWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Count
	count, err := s.Repo.CountMasterVendorGroupWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.MasterVendorGroupDto, len(result))
	for i, v := range result {
		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.MasterVendorGroupDto](v)
	}

	//NOTE Get latest sync date
	var latestSyncDate *time.Time
	if len(result) > 0 {
		latestSyncDate = result[0].LatestSyncDate
	} else {
		latestSyncDate, err = s.Repo.FindMasterVendorGroupLatestSyncDate()
		if err != nil {
			return resp, errs.NewError(http.StatusInternalServerError, err)
		}
	}

	//NOTE - Response
	resp = dto.MasterVendorGroupPageRespDto[dto.MasterVendorGroupDto]{
		PagingModel:    *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
		LatestSyncDate: latestSyncDate,
	}

	return resp, nil
}

func (s *masterVendorGroupService) UpdateMasterVendorGroupStatus(req dto.MasterVendorGroupUpdateReqDto) error {
	fieldsToUpdate := map[string]interface{}{
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": util.Now(),
	}

	affectedRows, err := s.Repo.UpdatesMasterVendorGroupFieldsWhere(fieldsToUpdate, "id = ?", req.Id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(fiber.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}

	return nil
}

func (s *masterVendorGroupService) SyncMasterVendorGroupFromErp(actionBy *int, erpConfig global.ErpConfig, authConfig global.AuthConfig) error {
	erpMap, allKeys, err := s.getVendorGroupFromErp(erpConfig, authConfig)
	if err != nil {
		return err
	}
	dbMap, err := s.getVendorGroupFromDb(allKeys)
	if err != nil {
		return err
	}
	err = s.syncVendorGroup(actionBy, erpMap, dbMap, allKeys)
	if err != nil {
		return err
	}

	return nil
}

func (s *masterVendorGroupService) getVendorGroupFromErp(erpConfig global.ErpConfig, authConfig global.AuthConfig) (map[string]dto.MasterVendorGroupDto, map[string]struct{}, error) {
	erpList, err := erp.FetchListFromErp[dto.MasterVendorGroupSyncErpRespDto](
		erpConfig.VendorGroupUrl,
		authConfig.LoginUrl,
		authConfig.Email,
		authConfig.Password,
		map[string]interface{}{"Company_Name": "AUCT"},
		"data",
	)
	if err != nil {
		return nil, nil, errs.NewError(http.StatusInternalServerError, err)
	}

	erpMap := make(map[string]dto.MasterVendorGroupDto)
	allKeys := make(map[string]struct{})

	for _, e := range erpList {
		erpMap[util.Val(e.VendorGroupCode)] = dto.MasterVendorGroupDto{
			VendorGroupCode: e.VendorGroupCode,
			DescriptionTh:   e.DescriptionTh,
			DescriptionEn:   e.DescriptionEn,
			IsActive:        e.Status == "Active",
		}
		allKeys[util.Val(e.VendorGroupCode)] = struct{}{}
	}
	return erpMap, allKeys, nil
}

func (s *masterVendorGroupService) getVendorGroupFromDb(allKeys map[string]struct{}) (map[string]entity.MasterVendorGroup, error) {
	dbList, err := s.Repo.FindMasterVendorGroupAll()
	if err != nil {
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	dbMap := make(map[string]entity.MasterVendorGroup)
	for _, e := range dbList {
		dbMap[util.Val(e.VendorGroupCode)] = e
		allKeys[util.Val(e.VendorGroupCode)] = struct{}{}
	}
	return dbMap, nil
}

func (s *masterVendorGroupService) syncVendorGroup(actionBy *int, erpMap map[string]dto.MasterVendorGroupDto, dbMap map[string]entity.MasterVendorGroup, allKeys map[string]struct{}) error {
	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		repo := repository.NewMasterVendorGroupRepository(tx)
		var toInsert []entity.MasterVendorGroup
		currentDateTime := util.NowPtr()

		for code := range allKeys {
			erp, erpExists := erpMap[code]
			db, dbExists := dbMap[code]

			if erpExists && dbExists {
				// มีทั้ง ERP + DB → compare → update หมด
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.VendorGroupCode = erp.VendorGroupCode
				temp.DescriptionTh = erp.DescriptionTh
				temp.DescriptionEn = erp.DescriptionEn
				temp.IsActive = erp.IsActive
				temp.IsDeletedByErp = false
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterVendorGroupAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}

			} else if erpExists && !dbExists {
				// มีใน ERP แต่ไม่มีใน DB → insert
				temp := entity.MasterVendorGroup{
					BaseEntity: &model.BaseEntity{
						CreatedDate: *currentDateTime,
						CreatedBy:   actionBy,
						UpdatedDate: currentDateTime,
						UpdatedBy:   actionBy,
					},
					VendorGroupCode: erp.VendorGroupCode,
					DescriptionTh:   erp.DescriptionTh,
					DescriptionEn:   erp.DescriptionEn,
					IsActive:        erp.IsActive,
					IsDeletedByErp:  false,
					LatestSyncDate:  currentDateTime,
				}
				toInsert = append(toInsert, temp)

			} else if !erpExists && dbExists {
				// มีใน DB แต่ไม่มีใน ERP → mark delete
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.IsActive = false
				temp.IsDeletedByErp = true
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterVendorGroupAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}
			}
		}

		if len(toInsert) > 0 {
			if err := repo.InsertMasterVendorGroupList(toInsert); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}
		return nil
	})
}
