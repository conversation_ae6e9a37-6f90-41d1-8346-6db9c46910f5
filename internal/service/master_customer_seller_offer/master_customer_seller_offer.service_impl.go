package service

import (
	"fmt"
	"net/http"
	"time"

	"backend-common-lib/constant"
	"backend-common-lib/erp"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/global"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	repository "content-service/internal/repository/master_customer_seller_offer"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func (s *masterCustomerSellerOfferService) SearchMasterCustomerSellerOfferFilter(req dto.MasterCustomerSellerOfferPageReqDto) (dto.MasterCustomerSellerOfferPageRespDto[dto.MasterCustomerSellerOfferDto], error) {
	resp := dto.MasterCustomerSellerOfferPageRespDto[dto.MasterCustomerSellerOfferDto]{}
	result, err := s.Repo.FindMasterCustomerSellerOfferWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Count
	count, err := s.Repo.CountMasterCustomerSellerOfferWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.MasterCustomerSellerOfferDto, len(result))
	for i, v := range result {
		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.MasterCustomerSellerOfferDto](v)
	}

	//NOTE Get latest sync date
	var latestSyncDate *time.Time
	if len(result) > 0 {
		latestSyncDate = result[0].LatestSyncDate
	} else {
		latestSyncDate, err = s.Repo.FindMasterCustomerSellerOfferLatestSyncDate()
		if err != nil {
			return resp, errs.NewError(http.StatusInternalServerError, err)
		}
	}

	//NOTE - Response
	resp = dto.MasterCustomerSellerOfferPageRespDto[dto.MasterCustomerSellerOfferDto]{
		PagingModel:    *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
		LatestSyncDate: latestSyncDate,
	}

	return resp, nil
}

func (s *masterCustomerSellerOfferService) UpdateMasterCustomerSellerOfferStatus(req dto.MasterCustomerSellerOfferUpdateReqDto) error {
	fieldsToUpdate := map[string]interface{}{
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": util.Now(),
	}

	affectedRows, err := s.Repo.UpdatesMasterCustomerSellerOfferFieldsWhere(fieldsToUpdate, "id = ?", req.Id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(fiber.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}

	return nil
}

func (s *masterCustomerSellerOfferService) SyncMasterCustomerSellerOfferFromErp(actionBy *int, erpConfig global.ErpConfig, authConfig global.AuthConfig) error {
	erpMap, allKeys, err := s.getCustomerSellerOfferFromErp(erpConfig, authConfig)
	if err != nil {
		return err
	}
	dbMap, err := s.getCustomerSellerOfferFromDb(allKeys)
	if err != nil {
		return err
	}
	err = s.syncCustomerSellerOffer(actionBy, erpMap, dbMap, allKeys)
	if err != nil {
		return err
	}

	return nil
}

func (s *masterCustomerSellerOfferService) getCustomerSellerOfferFromErp(erpConfig global.ErpConfig, authConfig global.AuthConfig) (map[string]dto.MasterCustomerSellerOfferDto, map[string]struct{}, error) {
	erpList, err := erp.FetchListFromErp[dto.MasterCustomerSellerOfferSyncErpRespDto](
		erpConfig.CustomerSellerOfferUrl,
		authConfig.LoginUrl,
		authConfig.Email,
		authConfig.Password,
		map[string]interface{}{"Company_Name": "AUCT"},
		"data",
	)

	if err != nil {
		return nil, nil, errs.NewError(http.StatusInternalServerError, err)
	}

	erpMap := make(map[string]dto.MasterCustomerSellerOfferDto)
	allKeys := make(map[string]struct{})

	for _, e := range erpList {

		erpMap[util.Val(e.CustomerNo)] = dto.MasterCustomerSellerOfferDto{
			CompanyCode:    e.CompanyCode,
			CustomerNo:     e.CustomerNo,
			SellerCode:     e.SellerCode,
			IsActive:       e.Status == "Active",
			IsDeletedByErp: false,
		}
		allKeys[util.Val(e.CustomerNo)] = struct{}{}
	}

	return erpMap, allKeys, nil
}

func (s *masterCustomerSellerOfferService) getCustomerSellerOfferFromDb(allKeys map[string]struct{}) (map[string]entity.MasterCustomerSellerOffer, error) {
	dbList, err := s.Repo.FindMasterCustomerSellerOfferAll()
	if err != nil {
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	dbMap := make(map[string]entity.MasterCustomerSellerOffer)
	for _, e := range dbList {
		dbMap[util.Val(e.CustomerNo)] = e
		allKeys[util.Val(e.CustomerNo)] = struct{}{}
	}
	return dbMap, nil
}

func (s *masterCustomerSellerOfferService) syncCustomerSellerOffer(actionBy *int, erpMap map[string]dto.MasterCustomerSellerOfferDto, dbMap map[string]entity.MasterCustomerSellerOffer, allKeys map[string]struct{}) error {
	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		repo := repository.NewMasterCustomerSellerOfferRepository(tx)
		var toInsert []entity.MasterCustomerSellerOffer
		currentDateTime := util.NowPtr()

		for code := range allKeys {
			erp, erpExists := erpMap[code]
			db, dbExists := dbMap[code]

			if erpExists && dbExists {
				// มีทั้ง ERP + DB → compare → update หมด
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.CompanyCode = erp.CompanyCode
				temp.CustomerNo = erp.CustomerNo
				temp.SellerCode = erp.SellerCode
				temp.IsActive = erp.IsActive
				temp.IsDeletedByErp = false
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterCustomerSellerOfferAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}

			} else if erpExists && !dbExists {
				// มีใน ERP แต่ไม่มีใน DB → insert
				temp := entity.MasterCustomerSellerOffer{
					BaseEntity: &model.BaseEntity{
						CreatedDate: *currentDateTime,
						CreatedBy:   actionBy,
						UpdatedDate: currentDateTime,
						UpdatedBy:   actionBy,
					},
					CompanyCode:    erp.CompanyCode,
					CustomerNo:     erp.CustomerNo,
					SellerCode:     erp.SellerCode,
					IsActive:       erp.IsActive,
					IsDeletedByErp: false,
					LatestSyncDate: currentDateTime,
				}
				toInsert = append(toInsert, temp)

			} else if !erpExists && dbExists {
				// มีใน DB แต่ไม่มีใน ERP → mark delete
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.IsActive = false
				temp.IsDeletedByErp = true
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterCustomerSellerOfferAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}
			}
		}

		if len(toInsert) > 0 {
			if err := repo.InsertMasterCustomerSellerOfferList(toInsert); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}
		return nil
	})
}
