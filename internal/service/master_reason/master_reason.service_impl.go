package service

import (
	"backend-common-lib/constant"
	"backend-common-lib/erp"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/global"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	repository "content-service/internal/repository/master_reason"
	"fmt"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func (s *masterReasonService) SearchMasterReasonFilter(req dto.MasterReasonPageReqDto) (dto.MasterReasonPageRespDto[dto.MasterReasonDto], error) {
	resp := dto.MasterReasonPageRespDto[dto.MasterReasonDto]{}
	result, err := s.Repo.FindMasterReasonWithFilter(req)
	if err != nil {
		return resp, errs.NewError(fiber.StatusInternalServerError, err)
	}

	//NOTE - Count
	count, err := s.Repo.CountMasterReasonWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.MasterReasonDto, len(result))
	for i, v := range result {
		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.MasterReasonDto](v)
	}

	//NOTE Get latest sync date
	var latestSyncDate *time.Time
	if len(result) > 0 {
		latestSyncDate = result[0].LatestSyncDate
	} else {
		latestSyncDate, err = s.Repo.FindLatestSyncDate()
		if err != nil {
			return resp, errs.NewError(fiber.StatusInternalServerError, err)
		}
	}

	//NOTE - Response
	resp = dto.MasterReasonPageRespDto[dto.MasterReasonDto]{
		PagingModel:    *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
		LatestSyncDate: latestSyncDate,
	}

	return resp, nil
}

func (s *masterReasonService) UpdateMasterReasonStatus(req dto.MasterReasonUpdateReqDto) error {
	fieldsToUpdate := map[string]interface{}{
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": util.Now(),
	}

	affectedRows, err := s.Repo.UpdatesMasterReasonFieldsWhere(fieldsToUpdate, "id = ?", req.Id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(fiber.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}

	return nil
}

func (s *masterReasonService) SyncMasterReasonFromErp(actionBy *int, erpConfig global.ErpConfig, authConfig global.AuthConfig) error {
	erpMap, allKeys, err := s.getReasonFromErp(erpConfig, authConfig)
	if err != nil {
		return err
	}
	dbMap, err := s.getReasonFromDb(allKeys)
	if err != nil {
		return err
	}
	err = s.syncReason(actionBy, erpMap, dbMap, allKeys)
	if err != nil {
		return err
	}

	return nil
}

func (s *masterReasonService) getReasonFromErp(erpConfig global.ErpConfig, authConfig global.AuthConfig) (map[string]dto.MasterReasonDto, map[string]struct{}, error) {
	erpList, err := erp.FetchListFromErp[dto.MasterReasonSyncErpRespDto](
		erpConfig.ReasonUrl,
		authConfig.LoginUrl,
		authConfig.Email,
		authConfig.Password,
		map[string]interface{}{"Company_Name": "AUCT"},
		"data",
	)
	if err != nil {
		return nil, nil, errs.NewError(http.StatusInternalServerError, err)
	}

	erpMap := make(map[string]dto.MasterReasonDto)
	allKeys := make(map[string]struct{})

	for _, e := range erpList {
		reasonCode := util.Val(e.ReasonCode)
		erpMap[reasonCode] = dto.MasterReasonDto{
			CompanyCode:           e.CompanyCode,
			ReasonCode:            e.ReasonCode,
			DescriptionTh:         e.DescriptionTh,
			DescriptionEn:         e.DescriptionEn,
			SaleProcessStatusCode: e.SaleProcessStatusCode,
			Type:                  e.Type,
			Page:                  e.Page,
			SellerPaymentOption:   e.SellerPaymentOption,
			IsActive:              e.Status == "Active",
		}
		allKeys[reasonCode] = struct{}{}
	}
	return erpMap, allKeys, nil
}

func (s *masterReasonService) getReasonFromDb(allKeys map[string]struct{}) (map[string]entity.MasterReason, error) {
	dbList, err := s.Repo.FindMasterReasonAll()
	if err != nil {
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	dbMap := make(map[string]entity.MasterReason)
	for _, e := range dbList {
		reasonCode := util.Val(e.ReasonCode)
		dbMap[reasonCode] = e
		allKeys[reasonCode] = struct{}{}
	}
	return dbMap, nil
}

func (s *masterReasonService) syncReason(actionBy *int, erpMap map[string]dto.MasterReasonDto, dbMap map[string]entity.MasterReason, allKeys map[string]struct{}) error {
	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		repo := repository.NewMasterReasonRepository(tx)
		var toInsert []entity.MasterReason
		currentDateTime := util.NowPtr()

		for code := range allKeys {
			erp, erpExists := erpMap[code]
			db, dbExists := dbMap[code]

			if erpExists && dbExists {
				// มีทั้ง ERP + DB → compare → update หมด
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.CompanyCode = erp.CompanyCode
				temp.ReasonCode = erp.ReasonCode
				temp.DescriptionTh = erp.DescriptionTh
				temp.DescriptionEn = erp.DescriptionEn
				temp.SaleProcessStatusCode = erp.SaleProcessStatusCode
				temp.Type = erp.Type
				temp.Page = erp.Page
				temp.SellerPaymentOption = erp.SellerPaymentOption
				temp.IsActive = erp.IsActive
				temp.IsDeletedByErp = false
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterReasonAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}

			} else if erpExists && !dbExists {
				// มีใน ERP แต่ไม่มีใน DB → insert
				temp := entity.MasterReason{
					BaseEntity: &model.BaseEntity{
						CreatedDate: *currentDateTime,
						CreatedBy:   actionBy,
						UpdatedDate: currentDateTime,
						UpdatedBy:   actionBy,
					},
					CompanyCode:           erp.CompanyCode,
					ReasonCode:            erp.ReasonCode,
					DescriptionTh:         erp.DescriptionTh,
					DescriptionEn:         erp.DescriptionEn,
					SaleProcessStatusCode: erp.SaleProcessStatusCode,
					Type:                  erp.Type,
					Page:                  erp.Page,
					SellerPaymentOption:   erp.SellerPaymentOption,
					IsActive:              erp.IsActive,
					IsDeletedByErp:        false,
					LatestSyncDate:        currentDateTime,
				}
				toInsert = append(toInsert, temp)

			} else if !erpExists && dbExists {
				// มีใน DB แต่ไม่มีใน ERP → mark delete
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.IsActive = false
				temp.IsDeletedByErp = true
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterReasonAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}
			}
		}

		if len(toInsert) > 0 {
			if err := repo.InsertMasterReasonList(toInsert); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}
		return nil
	})
}
