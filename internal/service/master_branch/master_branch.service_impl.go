package service

import (
	"backend-common-lib/constant"
	"backend-common-lib/erp"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/global"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	repository "content-service/internal/repository/master_branch"
	"fmt"
	"net/http"

	"time"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func (s *masterBranchService) GetMasterBranchAll() (dto.MasterBranchListDto, error) {
	resp := dto.MasterBranchListDto{}
	branches, err := s.Repo.FindMasterBranchAll()
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.MasterBranchDto, len(branches))
	for i, v := range branches {
		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.MasterBranchDto](v)
	}

	//NOTE - Response
	resp = dto.MasterBranchListDto{
		MasterBranchList: mapResult,
	}
	return resp, nil
}

func (s *masterBranchService) SearchMasterBranchFilter(req dto.MasterBranchPageReqDto) (dto.MasterBranchPageRespDto[dto.MasterBranchDto], error) {
	resp := dto.MasterBranchPageRespDto[dto.MasterBranchDto]{}
	result, err := s.Repo.FindMasterBranchWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Count
	count, err := s.Repo.CountMasterBranchWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.MasterBranchDto, len(result))
	for i, v := range result {
		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.MasterBranchDto](v)
	}

	//NOTE Get latest sync date
	var latestSyncDate *time.Time
	if len(result) > 0 {
		latestSyncDate = result[0].LatestSyncDate
	} else {
		latestSyncDate, err = s.Repo.FindMasterBranchLatestSyncDate()
		if err != nil {
			return resp, errs.NewError(http.StatusInternalServerError, err)
		}
	}

	//NOTE - Response
	resp = dto.MasterBranchPageRespDto[dto.MasterBranchDto]{
		PagingModel:    *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
		LatestSyncDate: latestSyncDate,
	}

	return resp, nil
}

func (s *masterBranchService) UpdateMasterBranchStatus(req dto.MasterBranchUpdateReqDto) error {
	fieldsToUpdate := map[string]interface{}{
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": util.Now(),
	}

	affectedRows, err := s.Repo.UpdatesMasterBranchFieldsWhere(fieldsToUpdate, "id = ?", req.Id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(fiber.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}

	return nil
}

func (s *masterBranchService) SyncMasterBranchFromErp(actionBy *int, erpConfig global.ErpConfig, authConfig global.AuthConfig) error {
	erpMap, allKeys, err := s.getBranchFromErp(erpConfig, authConfig)
	if err != nil {
		return err
	}
	dbMap, err := s.getBranchFromDb(allKeys)
	if err != nil {
		return err
	}
	err = s.syncBranch(actionBy, erpMap, dbMap, allKeys)
	if err != nil {
		return err
	}

	return nil
}

func (s *masterBranchService) getBranchFromErp(erpConfig global.ErpConfig, authConfig global.AuthConfig) (map[string]dto.MasterBranchDto, map[string]struct{}, error) {
	erpList, err := erp.FetchListFromErp[dto.MasterBranchSyncErpRespDto](
		erpConfig.BranchUrl,
		authConfig.LoginUrl,
		authConfig.Email,
		authConfig.Password,
		map[string]interface{}{"Company_Name": "AUCT"},
		"data",
	)
	if err != nil {
		return nil, nil, errs.NewError(http.StatusInternalServerError, err)
	}

	erpMap := make(map[string]dto.MasterBranchDto)
	allKeys := make(map[string]struct{})

	for _, e := range erpList {
		erpMap[util.Val(e.BranchCode)] = dto.MasterBranchDto{
			CompanyCode:       e.CompanyCode,
			BranchCode:        e.BranchCode,
			DescriptionTh:     e.DescriptionTh,
			DescriptionEn:     e.DescriptionEn,
			CountryCode:       e.CountryCode,
			RegionCode:        e.RegionCode,
			CityCode:          e.CityCode,
			PostCode:          e.PostCode,
			VATBusinessCode:   e.VATBusinessCode,
			AssetLocationCode: e.AssetLocationCode,
			IsActive:          e.Status == "Active",
		}
		allKeys[util.Val(e.BranchCode)] = struct{}{}
	}
	return erpMap, allKeys, nil
}

func (s *masterBranchService) getBranchFromDb(allKeys map[string]struct{}) (map[string]entity.MasterBranch, error) {
	dbList, err := s.Repo.FindMasterBranchAll()
	if err != nil {
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	dbMap := make(map[string]entity.MasterBranch)
	for _, e := range dbList {
		dbMap[util.Val(e.BranchCode)] = e
		allKeys[util.Val(e.BranchCode)] = struct{}{}
	}
	return dbMap, nil
}

func (s *masterBranchService) syncBranch(actionBy *int, erpMap map[string]dto.MasterBranchDto, dbMap map[string]entity.MasterBranch, allKeys map[string]struct{}) error {
	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		repo := repository.NewMasterBranchRepository(tx)
		var toInsert []entity.MasterBranch
		currentDateTime := util.NowPtr()

		for code := range allKeys {
			erp, erpExists := erpMap[code]
			db, dbExists := dbMap[code]

			if erpExists && dbExists {
				// มีทั้ง ERP + DB → compare → update หมด
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.CompanyCode = erp.CompanyCode
				temp.BranchCode = erp.BranchCode
				temp.DescriptionTh = erp.DescriptionTh
				temp.DescriptionEn = erp.DescriptionEn
				temp.CountryCode = erp.CountryCode
				temp.RegionCode = erp.RegionCode
				temp.CityCode = erp.CityCode
				temp.PostCode = erp.PostCode
				temp.VATBusinessCode = erp.VATBusinessCode
				temp.AssetLocationCode = erp.AssetLocationCode
				temp.IsActive = erp.IsActive
				temp.IsDeletedByErp = false
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterBranchAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}

			} else if erpExists && !dbExists {
				// มีใน ERP แต่ไม่มีใน DB → insert
				temp := entity.MasterBranch{
					BaseEntity: &model.BaseEntity{
						CreatedDate: *currentDateTime,
						CreatedBy:   actionBy,
						UpdatedDate: currentDateTime,
						UpdatedBy:   actionBy,
					},
					CompanyCode:       erp.CompanyCode,
					BranchCode:        erp.BranchCode,
					DescriptionTh:     erp.DescriptionTh,
					DescriptionEn:     erp.DescriptionEn,
					CountryCode:       erp.CountryCode,
					RegionCode:        erp.RegionCode,
					CityCode:          erp.CityCode,
					PostCode:          erp.PostCode,
					VATBusinessCode:   erp.VATBusinessCode,
					AssetLocationCode: erp.AssetLocationCode,
					IsActive:          erp.IsActive,
					IsDeletedByErp:    false,
					LatestSyncDate:    currentDateTime,
				}
				toInsert = append(toInsert, temp)

			} else if !erpExists && dbExists {
				// มีใน DB แต่ไม่มีใน ERP → mark delete
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.IsActive = false
				temp.IsDeletedByErp = true
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterBranchAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}
			}
		}

		if len(toInsert) > 0 {
			if err := repo.InsertMasterBranchList(toInsert); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}
		return nil
	})
}
