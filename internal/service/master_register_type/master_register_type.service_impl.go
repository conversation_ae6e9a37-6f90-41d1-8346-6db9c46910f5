package service

import (
	"fmt"
	"net/http"
	"time"

	"backend-common-lib/constant"
	"backend-common-lib/erp"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/global"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	repository "content-service/internal/repository/master_register_type"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func (s *masterRegisterTypeService) SearchMasterRegisterTypeFilter(req dto.MasterRegisterTypePageReqDto) (dto.MasterRegisterTypePageRespDto[dto.MasterRegisterTypeDto], error) {
	resp := dto.MasterRegisterTypePageRespDto[dto.MasterRegisterTypeDto]{}
	result, err := s.Repo.FindMasterRegisterTypeWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Count
	count, err := s.Repo.CountMasterRegisterTypeWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.MasterRegisterTypeDto, len(result))
	for i, v := range result {
		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.MasterRegisterTypeDto](v)
	}

	//NOTE Get latest sync date
	var latestSyncDate *time.Time
	if len(result) > 0 {
		latestSyncDate = result[0].LatestSyncDate
	} else {
		latestSyncDate, err = s.Repo.FindMasterRegisterTypeLatestSyncDate()
		if err != nil {
			return resp, errs.NewError(http.StatusInternalServerError, err)
		}
	}

	//NOTE - Response
	resp = dto.MasterRegisterTypePageRespDto[dto.MasterRegisterTypeDto]{
		PagingModel:    *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
		LatestSyncDate: latestSyncDate,
	}

	return resp, nil
}

func (s *masterRegisterTypeService) UpdateMasterRegisterTypeStatus(req dto.MasterRegisterTypeUpdateReqDto) error {
	fieldsToUpdate := map[string]interface{}{
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": util.Now(),
	}

	affectedRows, err := s.Repo.UpdatesMasterRegisterTypeFieldsWhere(fieldsToUpdate, "id = ?", req.Id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(fiber.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}

	return nil
}

func (s *masterRegisterTypeService) SyncMasterRegisterTypeFromErp(actionBy *int, erpConfig global.ErpConfig, authConfig global.AuthConfig) error {
	erpMap, allKeys, err := s.getRegisterTypeFromErp(erpConfig, authConfig)
	if err != nil {
		return err
	}
	dbMap, err := s.getRegisterTypeFromDb(allKeys)
	if err != nil {
		return err
	}
	err = s.syncRegisterType(actionBy, erpMap, dbMap, allKeys)
	if err != nil {
		return err
	}

	return nil
}

func (s *masterRegisterTypeService) getRegisterTypeFromErp(erpConfig global.ErpConfig, authConfig global.AuthConfig) (map[string]dto.MasterRegisterTypeDto, map[string]struct{}, error) {
	erpList, err := erp.FetchListFromErp[dto.MasterRegisterTypeSyncErpRespDto](
		erpConfig.RegisterTypeUrl,
		authConfig.LoginUrl,
		authConfig.Email,
		authConfig.Password,
		map[string]interface{}{"Company_Name": "AUCT"},
		"data",
	)

	if err != nil {
		return nil, nil, errs.NewError(http.StatusInternalServerError, err)
	}

	erpMap := make(map[string]dto.MasterRegisterTypeDto)
	allKeys := make(map[string]struct{})

	for _, e := range erpList {
		var lineNoStr string
		if e.LineNo != nil {
			lineNoStr = fmt.Sprintf("%d", *e.LineNo)
		} else {
			lineNoStr = ""
		}

		erpMap[util.Val(e.AttributeCode)+util.Val(&lineNoStr)] = dto.MasterRegisterTypeDto{
			CompanyCode:    e.CompanyCode,
			IsActive:       e.Status == "Active",
			AttributeCode:  e.AttributeCode,
			AttributeName:  e.AttributeName,
			LineNo:         e.LineNo,
			OptionTh:       e.OptionTh,
			OptionEn:       e.OptionEn,
			IsDeletedByErp: false,
		}
		allKeys[util.Val(e.AttributeCode)+util.Val(&lineNoStr)] = struct{}{}
	}

	return erpMap, allKeys, nil
}

func (s *masterRegisterTypeService) getRegisterTypeFromDb(allKeys map[string]struct{}) (map[string]entity.MasterRegisterType, error) {
	dbList, err := s.Repo.FindMasterRegisterTypeAll()
	if err != nil {
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	dbMap := make(map[string]entity.MasterRegisterType)
	for _, e := range dbList {
		var lineNoStr string
		if e.LineNo != nil {
			lineNoStr = fmt.Sprintf("%d", *e.LineNo)
		} else {
			lineNoStr = ""
		}
		dbMap[util.Val(e.AttributeCode)+util.Val(&lineNoStr)] = e
		allKeys[util.Val(e.AttributeCode)+util.Val(&lineNoStr)] = struct{}{}
	}
	return dbMap, nil
}

func (s *masterRegisterTypeService) syncRegisterType(actionBy *int, erpMap map[string]dto.MasterRegisterTypeDto, dbMap map[string]entity.MasterRegisterType, allKeys map[string]struct{}) error {
	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		repo := repository.NewMasterRegisterTypeRepository(tx)
		var toInsert []entity.MasterRegisterType
		currentDateTime := util.NowPtr()

		for code := range allKeys {
			erp, erpExists := erpMap[code]
			db, dbExists := dbMap[code]

			if erpExists && dbExists {
				// มีทั้ง ERP + DB → compare → update หมด
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.CompanyCode = erp.CompanyCode
				temp.AttributeCode = erp.AttributeCode
				temp.LineNo = erp.LineNo
				temp.OptionTh = erp.OptionTh
				temp.OptionEn = erp.OptionEn
				temp.IsActive = erp.IsActive
				temp.IsDeletedByErp = false
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterRegisterTypeAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}

			} else if erpExists && !dbExists {
				// มีใน ERP แต่ไม่มีใน DB → insert
				temp := entity.MasterRegisterType{
					BaseEntity: &model.BaseEntity{
						CreatedDate: *currentDateTime,
						CreatedBy:   actionBy,
						UpdatedDate: currentDateTime,
						UpdatedBy:   actionBy,
					},
					CompanyCode:    erp.CompanyCode,
					AttributeCode:  erp.AttributeCode,
					LineNo:         erp.LineNo,
					OptionTh:       erp.OptionTh,
					OptionEn:       erp.OptionEn,
					IsActive:       erp.IsActive,
					IsDeletedByErp: false,
					LatestSyncDate: currentDateTime,
				}
				toInsert = append(toInsert, temp)

			} else if !erpExists && dbExists {
				// มีใน DB แต่ไม่มีใน ERP → mark delete
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.IsActive = false
				temp.IsDeletedByErp = true
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterRegisterTypeAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}
			}
		}

		if len(toInsert) > 0 {
			if err := repo.InsertMasterRegisterTypeList(toInsert); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}
		return nil
	})
}
