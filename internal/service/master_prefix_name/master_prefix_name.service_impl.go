package service

import (
	"backend-common-lib/constant"
	"backend-common-lib/erp"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/global"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	repository "content-service/internal/repository/master_prefix_name"
	"fmt"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func (s *masterPrefixNameService) SearchMasterPrefixNameFilter(req dto.MasterPrefixNamePageReqDto) (dto.MasterPrefixNamePageRespDto[dto.MasterPrefixNameDto], error) {
	resp := dto.MasterPrefixNamePageRespDto[dto.MasterPrefixNameDto]{}
	result, err := s.Repo.FindMasterPrefixNameWithFilter(req)
	if err != nil {
		return resp, errs.NewError(fiber.StatusInternalServerError, err)
	}

	//NOTE - Count
	count, err := s.Repo.CountMasterPrefixNameWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.MasterPrefixNameDto, len(result))
	for i, v := range result {
		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.MasterPrefixNameDto](v)
	}

	//NOTE Get latest sync date
	var latestSyncDate *time.Time
	if len(result) > 0 {
		latestSyncDate = result[0].LatestSyncDate
	} else {
		latestSyncDate, err = s.Repo.FindLatestSyncDate()
		if err != nil {
			return resp, errs.NewError(fiber.StatusInternalServerError, err)
		}
	}

	//NOTE - Response
	resp = dto.MasterPrefixNamePageRespDto[dto.MasterPrefixNameDto]{
		PagingModel:    *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
		LatestSyncDate: latestSyncDate,
	}

	return resp, nil
}

func (s *masterPrefixNameService) UpdateMasterPrefixNameStatus(req dto.MasterPrefixNameUpdateReqDto) error {
	fieldsToUpdate := map[string]interface{}{
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": util.Now(),
	}

	affectedRows, err := s.Repo.UpdatesMasterPrefixNameFieldsWhere(fieldsToUpdate, "id = ?", req.Id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(fiber.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}

	return nil
}

func (s *masterPrefixNameService) SyncMasterPrefixNameFromErp(actionBy *int, erpConfig global.ErpConfig, authConfig global.AuthConfig) error {
	erpMap, allKeys, err := s.getPrefixNameFromErp(erpConfig, authConfig)
	if err != nil {
		return err
	}
	dbMap, err := s.getPrefixNameFromDb(allKeys)
	if err != nil {
		return err
	}
	err = s.syncPrefixName(actionBy, erpMap, dbMap, allKeys)
	if err != nil {
		return err
	}

	return nil
}

func (s *masterPrefixNameService) getPrefixNameFromErp(erpConfig global.ErpConfig, authConfig global.AuthConfig) (map[string]dto.MasterPrefixNameDto, map[string]struct{}, error) {
	erpList, err := erp.FetchListFromErp[dto.MasterPrefixNameSyncErpRespDto](
		erpConfig.PrefixNameUrl,
		authConfig.LoginUrl,
		authConfig.Email,
		authConfig.Password,
		map[string]interface{}{"Company_Name": "AUCT"},
		"data",
	)
	if err != nil {
		return nil, nil, errs.NewError(http.StatusInternalServerError, err)
	}

	erpMap := make(map[string]dto.MasterPrefixNameDto)
	allKeys := make(map[string]struct{})

	for _, e := range erpList {
		prefixNameCode := util.Val(e.PrefixNameCode)
		erpMap[prefixNameCode] = dto.MasterPrefixNameDto{
			CompanyCode:    e.CompanyCode,
			PrefixNameCode: e.PrefixNameCode,
			DescriptionTh:  e.DescriptionTh,
			DescriptionEn:  e.DescriptionEn,
			IsActive:       e.Status == "Active",
		}
		allKeys[prefixNameCode] = struct{}{}
	}
	return erpMap, allKeys, nil
}

func (s *masterPrefixNameService) getPrefixNameFromDb(allKeys map[string]struct{}) (map[string]entity.MasterPrefixName, error) {
	dbList, err := s.Repo.FindMasterPrefixNameAll()
	if err != nil {
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	dbMap := make(map[string]entity.MasterPrefixName)
	for _, e := range dbList {
		prefixNameCode := util.Val(e.PrefixNameCode)
		dbMap[prefixNameCode] = e
		allKeys[prefixNameCode] = struct{}{}
	}
	return dbMap, nil
}

func (s *masterPrefixNameService) syncPrefixName(actionBy *int, erpMap map[string]dto.MasterPrefixNameDto, dbMap map[string]entity.MasterPrefixName, allKeys map[string]struct{}) error {
	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		repo := repository.NewMasterPrefixNameRepository(tx)
		var toInsert []entity.MasterPrefixName
		currentDateTime := util.NowPtr()

		for code := range allKeys {
			erp, erpExists := erpMap[code]
			db, dbExists := dbMap[code]

			if erpExists && dbExists {
				// มีทั้ง ERP + DB → compare → update หมด
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.PrefixNameCode = erp.PrefixNameCode
				temp.DescriptionTh = erp.DescriptionTh
				temp.DescriptionEn = erp.DescriptionEn
				temp.IsActive = erp.IsActive
				temp.IsDeletedByErp = false
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterPrefixNameAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}

			} else if erpExists && !dbExists {
				// มีใน ERP แต่ไม่มีใน DB → insert
				temp := entity.MasterPrefixName{
					BaseEntity: &model.BaseEntity{
						CreatedDate: *currentDateTime,
						CreatedBy:   actionBy,
						UpdatedDate: currentDateTime,
						UpdatedBy:   actionBy,
					},
					CompanyCode:    erp.CompanyCode,
					PrefixNameCode: erp.PrefixNameCode,
					DescriptionTh:  erp.DescriptionTh,
					DescriptionEn:  erp.DescriptionEn,
					IsActive:       erp.IsActive,
					IsDeletedByErp: false,
					LatestSyncDate: currentDateTime,
				}
				toInsert = append(toInsert, temp)

			} else if !erpExists && dbExists {
				// มีใน DB แต่ไม่มีใน ERP → mark delete
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.IsActive = false
				temp.IsDeletedByErp = true
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterPrefixNameAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}
			}
		}

		if len(toInsert) > 0 {
			if err := repo.InsertMasterPrefixNameList(toInsert); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}
		return nil
	})
}
