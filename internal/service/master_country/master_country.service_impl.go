package service

import (
	"backend-common-lib/constant"
	"backend-common-lib/erp"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/global"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	repository "content-service/internal/repository/master_country"
	"fmt"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func (s *masterCountryService) SearchMasterCountryFilter(req dto.MasterCountryPageReqDto) (dto.MasterCountryPageRespDto[dto.MasterCountryDto], error) {
	resp := dto.MasterCountryPageRespDto[dto.MasterCountryDto]{}
	result, err := s.Repo.FindMasterCountryWithFilter(req)
	if err != nil {
		return resp, errs.NewError(fiber.StatusInternalServerError, err)
	}

	//NOTE - Count
	count, err := s.Repo.CountMasterCountryWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.MasterCountryDto, len(result))
	for i, v := range result {
		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.MasterCountryDto](v)
	}

	//NOTE Get latest sync date
	var latestSyncDate *time.Time
	if len(result) > 0 {
		latestSyncDate = result[0].LatestSyncDate
	} else {
		latestSyncDate, err = s.Repo.FindLatestSyncDate()
		if err != nil {
			return resp, errs.NewError(fiber.StatusInternalServerError, err)
		}
	}

	//NOTE - Response
	resp = dto.MasterCountryPageRespDto[dto.MasterCountryDto]{
		PagingModel:    *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
		LatestSyncDate: latestSyncDate,
	}

	return resp, nil
}

func (s *masterCountryService) UpdateMasterCountryStatus(req dto.MasterCountryUpdateReqDto) error {
	fieldsToUpdate := map[string]interface{}{
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": util.Now(),
	}

	affectedRows, err := s.Repo.UpdatesMasterCountryFieldsWhere(fieldsToUpdate, "id = ?", req.Id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(fiber.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}

	return nil
}

func (s *masterCountryService) SyncMasterCountryFromErp(actionBy *int, erpConfig global.ErpConfig, authConfig global.AuthConfig) error {
	erpMap, allKeys, err := s.getCountryFromErp(erpConfig, authConfig)
	if err != nil {
		return err
	}
	dbMap, err := s.getCountryFromDb(allKeys)
	if err != nil {
		return err
	}

	err = s.syncCountry(actionBy, erpMap, dbMap, allKeys)
	if err != nil {
		return err
	}

	return nil
}

func (s *masterCountryService) getCountryFromErp(erpConfig global.ErpConfig, authConfig global.AuthConfig) (map[string]dto.MasterCountryDto, map[string]struct{}, error) {
	erpList, err := erp.FetchListFromErp[dto.MasterCountrySyncErpRespDto](
		erpConfig.CountryUrl,
		authConfig.LoginUrl,
		authConfig.Email,
		authConfig.Password,
		map[string]interface{}{"Company_Name": "AUCT"},
		"data",
	)
	if err != nil {
		return nil, nil, errs.NewError(http.StatusInternalServerError, err)
	}

	erpMap := make(map[string]dto.MasterCountryDto)
	allKeys := make(map[string]struct{})

	for _, e := range erpList {
		countryCode := util.Val(e.CountryCode)
		erpMap[countryCode] = dto.MasterCountryDto{
			CompanyCode:   e.CompanyCode,
			CountryCode:   e.CountryCode,
			DescriptionTh: e.DescriptionTh,
			DescriptionEn: e.DescriptionEn,
			NationalityTh: e.NationalityTh,
			NationalityEn: e.NationalityEn,
			IsActive:      e.Status == "Active",
		}
		allKeys[countryCode] = struct{}{}
	}
	return erpMap, allKeys, nil
}

func (s *masterCountryService) getCountryFromDb(allKeys map[string]struct{}) (map[string]entity.MasterCountry, error) {
	dbList, err := s.Repo.FindMasterCountryAll()
	if err != nil {
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	dbMap := make(map[string]entity.MasterCountry)
	for _, e := range dbList {
		countryCode := util.Val(e.CountryCode)
		dbMap[countryCode] = e
		allKeys[countryCode] = struct{}{}
	}
	return dbMap, nil
}

func (s *masterCountryService) syncCountry(actionBy *int, erpMap map[string]dto.MasterCountryDto, dbMap map[string]entity.MasterCountry, allKeys map[string]struct{}) error {
	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		repo := repository.NewMasterCountryRepository(tx)
		var toInsert []entity.MasterCountry
		currentDateTime := util.NowPtr()

		for code := range allKeys {
			erp, erpExists := erpMap[code]
			db, dbExists := dbMap[code]

			if erpExists && dbExists {
				// มีทั้ง ERP + DB → compare → update หมด
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.CountryCode = erp.CountryCode
				temp.DescriptionTh = erp.DescriptionTh
				temp.DescriptionEn = erp.DescriptionEn
				temp.NationalityTh = erp.NationalityTh
				temp.NationalityEn = erp.NationalityEn
				temp.IsActive = erp.IsActive
				temp.IsDeletedByErp = false
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterCountryAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}

			} else if erpExists && !dbExists {
				// มีใน ERP แต่ไม่มีใน DB → insert
				temp := entity.MasterCountry{
					BaseEntity: &model.BaseEntity{
						CreatedDate: *currentDateTime,
						CreatedBy:   actionBy,
						UpdatedDate: currentDateTime,
						UpdatedBy:   actionBy,
					},
					CompanyCode:    erp.CompanyCode,
					CountryCode:    erp.CountryCode,
					DescriptionTh:  erp.DescriptionTh,
					DescriptionEn:  erp.DescriptionEn,
					NationalityTh:  erp.NationalityTh,
					NationalityEn:  erp.NationalityEn,
					IsActive:       erp.IsActive,
					IsDeletedByErp: false,
					LatestSyncDate: currentDateTime,
				}
				toInsert = append(toInsert, temp)

			} else if !erpExists && dbExists {
				// มีใน DB แต่ไม่มีใน ERP → mark delete
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.IsActive = false
				temp.IsDeletedByErp = true
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterCountryAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}
			}
		}

		if len(toInsert) > 0 {
			if err := repo.InsertMasterCountryList(toInsert); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}
		return nil
	})
}
