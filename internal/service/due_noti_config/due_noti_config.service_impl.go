package service

import (
	"backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"net/http"

	"gorm.io/gorm"
)

func (s *dueNotiConfigService) GetDueNotiConfig(req model.PagingRequest, notiType string) (model.PagingModel[dto.DueNotiConfigDto], error) {
	resp := model.PagingModel[dto.DueNotiConfigDto]{}

	//NOTE - Get List
	result, err := s.Repo.FindAllDueNotiConfig(req, notiType)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Count
	count, err := s.Repo.CountAllDueNotiConfig(notiType)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Get Top 4 Buyer of each record
	var ids []int
	for _, n := range result {
		ids = append(ids, n.BaseEntity.Id)
	}

	buyersMap, err := s.ExcludedBuyerRepo.GetTopExcludedBuyersByNotificationIds(ids, 4)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	dtoList := make([]dto.DueNotiConfigDto, 0, len(result))
	for _, entity := range result {
		dtoResult := dto.DueNotiConfigDto{}

		dtoResult = util.MapToWithCreatedByAndUpdatedBy[dto.DueNotiConfigDto](entity)
		for _, buyer := range buyersMap[entity.BaseEntity.Id] {
			if buyer.Buyer != nil {
				dtoResult.ExcludedBidder = append(dtoResult.ExcludedBidder, &dto.ExcludedBidderDto{
					BuyerId:  buyer.BuyerId,
					BidderId: buyer.Buyer.BidderId,
				})
			}
		}
		dtoList = append(dtoList, dtoResult)

	}

	//NOTE - Response
	resp = util.Val(util.MapPaginationResult(dtoList, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit))

	return resp, nil
}

func (s *dueNotiConfigService) GetDueNotiConfigByID(id int) (dto.DueNotiConfigDto, error) {
	result, err := s.Repo.FindDueNotiConfigByID(id)
	if err != nil {
		if errs.IsGormNotFound(err) {
			return dto.DueNotiConfigDto{}, errs.NewError(http.StatusNotFound, err)
		}
		return dto.DueNotiConfigDto{}, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Get Excluded Buyer of each record
	excludedBuyers, err := s.ExcludedBuyerRepo.GetExcludedBuyersByNotificationId(id)
	if err != nil {
		return dto.DueNotiConfigDto{}, errs.NewError(http.StatusInternalServerError, err)
	}

	dtoResult := dto.DueNotiConfigDto{}
	dtoResult = util.MapToWithCreatedByAndUpdatedBy[dto.DueNotiConfigDto](*result)
	for _, buyer := range excludedBuyers {
		if buyer.Buyer != nil {
			dtoResult.ExcludedBidder = append(dtoResult.ExcludedBidder, &dto.ExcludedBidderDto{
				BuyerId:  buyer.BuyerId,
				BidderId: buyer.Buyer.BidderId,
			})
		}
	}

	return dtoResult, nil
}

func validateDueNotiConfig(dueNotiConfig dto.DueNotiConfigDto, action string) error {
	if util.Val(dueNotiConfig.CustomerGroupId) == 0 && action == constant.ActionMode.Create {
		return errs.NewBusinessError(http.StatusBadRequest, constant.ValueInvalidate, "customerGroupId is required", "")
	}
	if util.Val(dueNotiConfig.DaysBeforeDue) == 0 {
		return errs.NewBusinessError(http.StatusBadRequest, constant.ValueInvalidate, "daysBeforeDue is required", "")
	}
	return nil
}

func (s *dueNotiConfigService) CreateDueNotiConfig(req dto.DueNotiConfigDto, notiType string, actionBy *int) error {
	now := util.Now()

	//NOTE - Check Required fields
	err := validateDueNotiConfig(req, constant.ActionMode.Create)
	if err != nil {
		return err
	}

	//NOTE - Check for duplicate customer_group_id
	dupCheck, err := s.Repo.FindByCustomerGroupId(util.Val(req.CustomerGroupId), notiType)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if len(dupCheck) > 0 {
		return errs.NewBusinessError(http.StatusBadRequest, constant.Invalidate, "CustomerGroup ids duplicated", "error.dueNotiConfig.duplicateCustomerGroup")
	}

	//NOTE - Create Payment Due Notification
	entityDueNotiConfig := util.MapToPtr[entity.DueNotiConfig](req)
	if entityDueNotiConfig == nil {
		return errs.NewBusinessError(http.StatusBadRequest, constant.BadRequest, "Invalid request data", "")
	}

	entityDueNotiConfig.NotiType = util.Ptr(notiType)

	entityDueNotiConfig.BaseEntity = &model.BaseEntity{
		CreatedBy:   actionBy,
		CreatedDate: now,
		UpdatedBy:   actionBy,
		UpdatedDate: &now,
	}

	errTx := util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {

		err := s.Repo.InsertDueNotiConfig(entityDueNotiConfig)
		if err != nil {
			return errs.NewError(http.StatusInternalServerError, err)
		}

		excludedBuyers := make([]entity.DueNotiConfigExcludedBuyer, 0, len(req.ExcludedBidder))

		for _, buyer := range req.ExcludedBidder {
			excludedBuyer := entity.DueNotiConfigExcludedBuyer{
				DueNotiConfigId: entityDueNotiConfig.Id,
				BuyerId:         buyer.BuyerId,
			}
			excludedBuyers = append(excludedBuyers, excludedBuyer)
		}
		if len(excludedBuyers) > 0 {
			if err := s.ExcludedBuyerRepo.BulkInsertExcludedBuyers(excludedBuyers); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}
		return nil

	})
	if errTx != nil {
		return errTx
	}

	return nil
}

func (s *dueNotiConfigService) UpdateDueNotiConfig(req dto.DueNotiConfigDto, actionBy *int) error {
	now := util.Now()

	//NOTE - Check Required fields
	err := validateDueNotiConfig(req, constant.ActionMode.Update)
	if err != nil {
		return err
	}

	//NOTE - Update Payment Due Notification
	entityPaymentDue := util.MapToPtr[entity.DueNotiConfig](req)
	if entityPaymentDue == nil {
		return errs.NewBusinessError(http.StatusBadRequest, constant.BadRequest, "Invalid request data", "")
	}

	fieldsToUpdate := map[string]interface{}{
		"days_before_due": entityPaymentDue.DaysBeforeDue,
		"is_active":       entityPaymentDue.IsActive,
		"updated_by":      actionBy,
		"updated_date":    &now,
	}

	errTx := util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {

		//NOTE - Update Payment Due Notification
		rowsAffected, err := s.Repo.UpdateDueNotiConfig(req.Id, fieldsToUpdate)
		if err != nil {
			return errs.NewError(http.StatusInternalServerError, err)
		}
		if rowsAffected == 0 {
			return errs.NewError(http.StatusNotFound, err)
		}

		//NOTE - Permanently delete existing excluded buyers
		err = s.ExcludedBuyerRepo.PermanentDeleteExcludedBuyers(req.Id)
		if err != nil {
			return errs.NewError(http.StatusInternalServerError, err)
		}

		excludedBuyers := make([]entity.DueNotiConfigExcludedBuyer, 0, len(req.ExcludedBidder))

		for _, buyer := range req.ExcludedBidder {
			excludedBuyer := entity.DueNotiConfigExcludedBuyer{
				DueNotiConfigId: req.Id,
				BuyerId:         buyer.BuyerId,
			}
			excludedBuyers = append(excludedBuyers, excludedBuyer)
		}
		if len(excludedBuyers) > 0 {
			if err := s.ExcludedBuyerRepo.BulkInsertExcludedBuyers(excludedBuyers); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}
		return nil

	})
	if errTx != nil {
		return errTx
	}

	return nil
}

func (s *dueNotiConfigService) UpdateDueNotiConfigStatus(req dto.DueNotiConfigDto, actionBy *int) error {
	now := util.Now()

	fieldsToUpdate := map[string]interface{}{
		"is_active":    req.IsActive,
		"updated_by":   actionBy,
		"updated_date": &now,
	}

	errTx := util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {

		//NOTE - Update Payment Due Notification
		rowsAffected, err := s.Repo.UpdateDueNotiConfig(req.Id, fieldsToUpdate)
		if err != nil {
			return errs.NewError(http.StatusInternalServerError, err)
		}
		if rowsAffected == 0 {
			return errs.NewError(http.StatusNotFound, err)
		}
		return nil

	})
	if errTx != nil {
		return errTx
	}

	return nil
}

func (s *dueNotiConfigService) DeleteDueNotiConfig(id int) error {
	errTx := util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {

		//NOTE - Delete existing excluded buyers
		err := s.ExcludedBuyerRepo.DeleteExcludedBuyerNotificationId(id)
		if err != nil {
			return errs.NewError(http.StatusInternalServerError, err)
		}

		//NOTE - Delete Payment Due Notification
		rowsAffected, err := s.Repo.DeleteDueNotiConfig(id)
		if err != nil {
			return errs.NewError(http.StatusInternalServerError, err)
		}
		if rowsAffected == 0 {
			return errs.NewError(http.StatusNotFound, err)
		}
		return nil

	})
	if errTx != nil {
		return errTx
	}

	return nil
}
