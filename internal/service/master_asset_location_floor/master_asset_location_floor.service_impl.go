package service

import (
	"backend-common-lib/erp"
	"content-service/internal/global"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	repository "content-service/internal/repository/master_asset_location_floor"
	"fmt"
	"net/http"

	"time"

	"backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func (s *masterAssetLocationFloorService) SearchMasterAssetLocationFloorFilter(req dto.MasterAssetLocationFloorPageReqDto) (dto.MasterAssetLocationFloorPageRespDto[dto.MasterAssetLocationFloorDto], error) {
	resp := dto.MasterAssetLocationFloorPageRespDto[dto.MasterAssetLocationFloorDto]{}
	result, err := s.Repo.FindMasterAssetLocationFloorWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Count
	count, err := s.Repo.CountMasterAssetLocationFloorWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.MasterAssetLocationFloorDto, len(result))
	for i, v := range result {
		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.MasterAssetLocationFloorDto](v)
	}

	//NOTE Get latest sync date
	var latestSyncDate *time.Time
	if len(result) > 0 {
		latestSyncDate = result[0].LatestSyncDate
	} else {
		latestSyncDate, err = s.Repo.FindMasterAssetLocationFloorLatestSyncDate()
		if err != nil {
			return resp, errs.NewError(http.StatusInternalServerError, err)
		}
	}

	//NOTE - Response
	resp = dto.MasterAssetLocationFloorPageRespDto[dto.MasterAssetLocationFloorDto]{
		PagingModel:    *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
		LatestSyncDate: latestSyncDate,
	}

	return resp, nil
}

func (s *masterAssetLocationFloorService) UpdateMasterAssetLocationFloorStatus(req dto.MasterAssetLocationFloorUpdateReqDto) error {
	fieldsToUpdate := map[string]interface{}{
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": util.Now(),
	}

	affectedRows, err := s.Repo.UpdatesMasterAssetLocationFloorFieldsWhere(fieldsToUpdate, "id = ?", req.Id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(fiber.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}

	return nil
}

func (s *masterAssetLocationFloorService) SyncMasterAssetLocationFloorFromErp(actionBy *int, erpConfig global.ErpConfig, authConfig global.AuthConfig) error {
	erpMap, allKeys, err := s.getAssetLocationFloorFromErp(erpConfig, authConfig)
	if err != nil {
		return err
	}
	dbMap, err := s.getAssetLocationFloorFromDb(allKeys)
	if err != nil {
		return err
	}
	err = s.syncAssetLocationFloor(actionBy, erpMap, dbMap, allKeys)
	if err != nil {
		return err
	}

	return nil
}

func (s *masterAssetLocationFloorService) getAssetLocationFloorFromErp(erpConfig global.ErpConfig, authConfig global.AuthConfig) (map[string]dto.MasterAssetLocationFloorDto, map[string]struct{}, error) {
	erpList, err := erp.FetchListFromErp[dto.MasterAssetLocationFloorSyncErpRespDto](
		erpConfig.AssetLocationFloorUrl,
		authConfig.LoginUrl,
		authConfig.Email,
		authConfig.Password,
		map[string]interface{}{"Company_Name": "AUCT"},
		"data",
	)
	if err != nil {
		return nil, nil, errs.NewError(http.StatusInternalServerError, err)
	}

	erpMap := make(map[string]dto.MasterAssetLocationFloorDto)
	allKeys := make(map[string]struct{})

	for _, e := range erpList {
		erpMap[util.Val(e.LocationFloorCode)+util.Val(e.Floor)] = dto.MasterAssetLocationFloorDto{
			LocationFloorCode: e.LocationFloorCode,
			Floor:             e.Floor,
			DescriptionTH:     e.DescriptionTh,
			DescriptionEN:     e.DescriptionEn,
			BranchCode:        e.BranchCode,
			IsActive:          e.Status == "Active",
		}
		allKeys[util.Val(e.LocationFloorCode)+util.Val(e.Floor)] = struct{}{}
	}
	return erpMap, allKeys, nil
}

func (s *masterAssetLocationFloorService) getAssetLocationFloorFromDb(allKeys map[string]struct{}) (map[string]entity.MasterAssetLocationFloor, error) {
	dbList, err := s.Repo.FindMasterAssetLocationFloorAll()
	if err != nil {
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	dbMap := make(map[string]entity.MasterAssetLocationFloor)
	for _, e := range dbList {
		dbMap[util.Val(e.LocationFloorCode)+util.Val(e.Floor)] = e
		allKeys[util.Val(e.LocationFloorCode)+util.Val(e.Floor)] = struct{}{}
	}
	return dbMap, nil
}

func (s *masterAssetLocationFloorService) syncAssetLocationFloor(actionBy *int, erpMap map[string]dto.MasterAssetLocationFloorDto, dbMap map[string]entity.MasterAssetLocationFloor, allKeys map[string]struct{}) error {
	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		repo := repository.NewMasterAssetLocationFloorRepository(tx)
		var toInsert []entity.MasterAssetLocationFloor
		currentDateTime := util.NowPtr()

		for code := range allKeys {
			erp, erpExists := erpMap[code]
			db, dbExists := dbMap[code]

			if erpExists && dbExists {
				// มีทั้ง ERP + DB → compare → update หมด
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.LocationFloorCode = erp.LocationFloorCode
				temp.Floor = erp.Floor
				temp.DescriptionTH = erp.DescriptionTH
				temp.DescriptionEN = erp.DescriptionEN
				temp.BranchCode = erp.BranchCode
				temp.IsActive = erp.IsActive
				temp.IsDeletedByErp = false
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterAssetLocationFloorAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}

			} else if erpExists && !dbExists {
				// มีใน ERP แต่ไม่มีใน DB → insert
				temp := entity.MasterAssetLocationFloor{
					BaseEntity: &model.BaseEntity{
						CreatedDate: *currentDateTime,
						CreatedBy:   actionBy,
						UpdatedDate: currentDateTime,
						UpdatedBy:   actionBy,
					},
					LocationFloorCode: erp.LocationFloorCode,
					Floor:             erp.Floor,
					DescriptionTH:     erp.DescriptionTH,
					DescriptionEN:     erp.DescriptionEN,
					BranchCode:        erp.BranchCode,
					IsActive:          erp.IsActive,
					IsDeletedByErp:    false,
					LatestSyncDate:    currentDateTime,
				}
				toInsert = append(toInsert, temp)

			} else if !erpExists && dbExists {
				// มีใน DB แต่ไม่มีใน ERP → mark delete
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.IsActive = false
				temp.IsDeletedByErp = true
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterAssetLocationFloorAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}
			}
		}

		if len(toInsert) > 0 {
			if err := repo.InsertMasterAssetLocationFloorList(toInsert); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}
		return nil
	})
}
