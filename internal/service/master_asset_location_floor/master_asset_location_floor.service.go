package service

import (
	"content-service/internal/global"
	"content-service/internal/model/dto"
	repository "content-service/internal/repository/master_asset_location_floor"
)

type masterAssetLocationFloorService struct {
	Repo repository.MasterAssetLocationFloorRepository
}

type MasterAssetLocationFloorService interface {
	SearchMasterAssetLocationFloorFilter(req dto.MasterAssetLocationFloorPageReqDto) (dto.MasterAssetLocationFloorPageRespDto[dto.MasterAssetLocationFloorDto], error)
	UpdateMasterAssetLocationFloorStatus(req dto.MasterAssetLocationFloorUpdateReqDto) error
	SyncMasterAssetLocationFloorFromErp(actionBy *int, erpConfig global.ErpConfig, authConfig global.AuthConfig) error
}

func NewMasterAssetLocationFloorService(repo repository.MasterAssetLocationFloorRepository) MasterAssetLocationFloorService {
	return &masterAssetLocationFloorService{Repo: repo}
}
