package service

import (
	"backend-common-lib/erp"
	"content-service/internal/global"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	repository "content-service/internal/repository/master_customer_group"
	"fmt"
	"net/http"

	"time"

	"backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func (s *masterCustomerGroupService) SearchMasterCustomerGroupFilter(req dto.MasterCustomerGroupPageReqDto) (dto.MasterCustomerGroupPageRespDto[dto.MasterCustomerGroupDto], error) {
	resp := dto.MasterCustomerGroupPageRespDto[dto.MasterCustomerGroupDto]{}
	result, err := s.Repo.FindMasterCustomerGroupWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Count
	count, err := s.Repo.CountMasterCustomerGroupWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.MasterCustomerGroupDto, len(result))
	for i, v := range result {
		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.MasterCustomerGroupDto](v)
	}

	//NOTE Get latest sync date
	var latestSyncDate *time.Time
	if len(result) > 0 {
		latestSyncDate = result[0].LatestSyncDate
	} else {
		latestSyncDate, err = s.Repo.FindMasterCustomerGroupLatestSyncDate()
		if err != nil {
			return resp, errs.NewError(http.StatusInternalServerError, err)
		}
	}

	//NOTE - Response
	resp = dto.MasterCustomerGroupPageRespDto[dto.MasterCustomerGroupDto]{
		PagingModel:    *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
		LatestSyncDate: latestSyncDate,
	}

	return resp, nil
}

func (s *masterCustomerGroupService) UpdateMasterCustomerGroupStatus(req dto.MasterCustomerGroupUpdateReqDto) error {
	fieldsToUpdate := map[string]interface{}{
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": util.Now(),
	}

	affectedRows, err := s.Repo.UpdatesMasterCustomerGroupFieldsWhere(fieldsToUpdate, "id = ?", req.Id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(fiber.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}

	return nil
}

func (s *masterCustomerGroupService) SyncMasterCustomerGroupFromErp(actionBy *int, erpConfig global.ErpConfig, authConfig global.AuthConfig) error {
	erpMap, allKeys, err := s.getCustomerGroupFromErp(erpConfig, authConfig)
	if err != nil {
		return err
	}
	dbMap, err := s.getCustomerGroupFromDb(allKeys)
	if err != nil {
		return err
	}
	err = s.syncCustomerGroup(actionBy, erpMap, dbMap, allKeys)
	if err != nil {
		return err
	}

	return nil
}

func (s *masterCustomerGroupService) getCustomerGroupFromErp(erpConfig global.ErpConfig, authConfig global.AuthConfig) (map[string]dto.MasterCustomerGroupDto, map[string]struct{}, error) {
	erpList, err := erp.FetchListFromErp[dto.MasterCustomerGroupSyncErpRespDto](
		erpConfig.CustomerGroupUrl,
		authConfig.LoginUrl,
		authConfig.Email,
		authConfig.Password,
		map[string]interface{}{"Company_Name": "AUCT"},
		"data",
	)
	if err != nil {
		return nil, nil, errs.NewError(http.StatusInternalServerError, err)
	}

	erpMap := make(map[string]dto.MasterCustomerGroupDto)
	allKeys := make(map[string]struct{})

	for _, e := range erpList {
		erpMap[util.Val(e.CustomerGroupCode)] = dto.MasterCustomerGroupDto{
			CustomerGroupCode: e.CustomerGroupCode,
			DescriptionTh:     e.Description,
			IsActive:          e.Status == "Active",
		}
		allKeys[util.Val(e.CustomerGroupCode)] = struct{}{}
	}
	return erpMap, allKeys, nil
}

func (s *masterCustomerGroupService) getCustomerGroupFromDb(allKeys map[string]struct{}) (map[string]entity.MasterCustomerGroup, error) {
	dbList, err := s.Repo.FindMasterCustomerGroupAll()
	if err != nil {
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	dbMap := make(map[string]entity.MasterCustomerGroup)
	for _, e := range dbList {
		dbMap[util.Val(e.CustomerGroupCode)] = e
		allKeys[util.Val(e.CustomerGroupCode)] = struct{}{}
	}
	return dbMap, nil
}

func (s *masterCustomerGroupService) syncCustomerGroup(actionBy *int, erpMap map[string]dto.MasterCustomerGroupDto, dbMap map[string]entity.MasterCustomerGroup, allKeys map[string]struct{}) error {
	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		repo := repository.NewMasterCustomerGroupRepository(tx)
		var toInsert []entity.MasterCustomerGroup
		currentDateTime := util.NowPtr()

		for code := range allKeys {
			erp, erpExists := erpMap[code]
			db, dbExists := dbMap[code]

			if erpExists && dbExists {
				// มีทั้ง ERP + DB → compare → update หมด
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.CustomerGroupCode = erp.CustomerGroupCode
				temp.DescriptionTh = erp.DescriptionTh
				temp.IsActive = erp.IsActive
				temp.IsDeletedByErp = false
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterCustomerGroupAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}

			} else if erpExists && !dbExists {
				// มีใน ERP แต่ไม่มีใน DB → insert
				temp := entity.MasterCustomerGroup{
					BaseEntity: &model.BaseEntity{
						CreatedDate: *currentDateTime,
						CreatedBy:   actionBy,
						UpdatedDate: currentDateTime,
						UpdatedBy:   actionBy,
					},
					CustomerGroupCode: erp.CustomerGroupCode,
					DescriptionTh:     erp.DescriptionTh,
					IsActive:          erp.IsActive,
					IsDeletedByErp:    false,
					LatestSyncDate:    currentDateTime,
				}
				toInsert = append(toInsert, temp)

			} else if !erpExists && dbExists {
				// มีใน DB แต่ไม่มีใน ERP → mark delete
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.IsActive = false
				temp.IsDeletedByErp = true
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterCustomerGroupAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}
			}
		}

		if len(toInsert) > 0 {
			if err := repo.InsertMasterCustomerGroupList(toInsert); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}
		return nil
	})
}

func (s *masterCustomerGroupService) FindMasterCustomerGroupAll() ([]entity.MasterCustomerGroup, error) {
	result, err := s.Repo.FindMasterCustomerGroupAll()
	if err != nil {
		return result, errs.NewError(http.StatusInternalServerError, err)
	}

	return result, nil
}
