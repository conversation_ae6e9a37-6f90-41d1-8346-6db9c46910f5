package service

import (
	"backend-common-lib/errs"
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"net/http"
	"strings"

	"github.com/gofiber/fiber/v2/log"
)

func (s *memberService) SearchMemberWithFilter(req dto.MemberSearchReqDto) (dto.MemberPageRespDto[dto.MemberSearchRespDto], error) {

	buyerDbs, err := s.BuyerRepo.FindBuyerWithFilter(req)
	if err != nil {
		log.Error(err)
		return dto.MemberPageRespDto[dto.MemberSearchRespDto]{}, err
	}

	mapResult := make([]dto.MemberSearchRespDto, len(buyerDbs))
	for i, v := range buyerDbs {
		mapResult[i] = *util.MapToPtr[dto.MemberSearchRespDto](v)

		var parts []string
		if v.FirstName != nil && *v.FirstName != "" {
			parts = append(parts, *v.FirstName)
		}
		if v.MiddleName != nil && *v.MiddleName != "" {
			parts = append(parts, *v.MiddleName)
		}
		if v.LastName != nil && *v.LastName != "" {
			parts = append(parts, *v.LastName)
		}
		fullname := strings.Join(parts, " ")

		mapResult[i].Name = &fullname
		if v.CustomerGroupForJoin != nil {
			mapResult[i].CustomerGroup = v.CustomerGroupForJoin.DescriptionTh
		}
	}

	responseDtos := dto.MemberPageRespDto[dto.MemberSearchRespDto]{
		PagingModel: *util.MapPaginationResult(mapResult, len(mapResult), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
	}

	return responseDtos, nil
}

func (s *memberService) UpdateMemberStatus(req dto.UpdateMemberStatusReqDto) error {
	_, err := s.BuyerRepo.GetById(req.Id)
	if err != nil {
		log.Error(err)
		return errs.NewError(http.StatusInternalServerError, err)
	}

	fieldsToUpdate := map[string]interface{}{}

	if req.IsBlock != nil {
		fieldsToUpdate = map[string]interface{}{
			"is_block":               req.IsBlock,
			"reason_block_blacklist": req.Reason,
			"updated_by":             req.ActionBy,
			"updated_date":           util.Now(),
		}
	} else if req.IsBlacklist != nil {
		fieldsToUpdate = map[string]interface{}{
			"is_blacklist":           req.IsBlacklist,
			"reason_block_blacklist": req.Reason,
			"updated_by":             req.ActionBy,
			"updated_date":           util.Now(),
		}
	}

	err = s.BuyerRepo.UpdateStatus(req.Id, fieldsToUpdate)
	if err != nil {
		log.Error(err)
		return errs.NewError(http.StatusInternalServerError, err)
	}

	return nil
}
