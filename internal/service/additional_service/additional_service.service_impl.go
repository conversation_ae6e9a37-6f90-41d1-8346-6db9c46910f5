package service

import (
	"backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func (s *additionalServiceService) GetAdditionalService(req model.PagingRequest) (model.PagingModel[dto.AdditionalServiceDto], error) {
	resp := model.PagingModel[dto.AdditionalServiceDto]{}

	//NOTE - Get List
	result, err := s.Repo.FindAllAdditionalService(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Count
	count, err := s.Repo.CountAllAdditionalService()
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	var ids []int
	for _, n := range result {
		ids = append(ids, n.BaseEntity.Id)
	}

	assetMap, err := s.ServiceAssetRepo.GetAssetByAdditionalServiceIds(ids...)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	customerGroupMap, err := s.ServiceCustomerGroup.GetCustomerGroupByAdditionalServiceIds(ids...)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	providerMap, err := s.ServiceProviderRepo.GetProviderByAdditionalServiceIds(ids...)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	displayMap, err := s.ServiceDisplayRepo.GetDisplayByAdditionalServiceIds(ids...)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	// //NOTE - Map to DTO
	dtoList := make([]dto.AdditionalServiceDto, 0, len(result))
	for _, entity := range result {
		dtoResult := dto.AdditionalServiceDto{}
		dtoResult = util.MapToWithCreatedByAndUpdatedBy[dto.AdditionalServiceDto](entity)

		for _, asset := range assetMap[entity.BaseEntity.Id] {
			if asset.AssetType != nil && asset.AssetGroup == nil {

				dtoResult.ServiceAssetType = append(dtoResult.ServiceAssetType, &dto.ServiceAssetType{
					AssetTypeId:     asset.AssetTypeId,
					AssetTypeDescTh: util.Ptr(asset.AssetType.DescriptionTh),
					AssetTypeDescEn: util.Ptr(asset.AssetType.DescriptionEn),
				})
			} else if asset.AssetGroup != nil {
				dtoResult.ServiceAssetGroup = append(dtoResult.ServiceAssetGroup, &dto.ServiceAssetGroup{
					AssetGroupId:     asset.AssetGroupId,
					AssetGroupDescTh: util.Ptr(asset.AssetGroup.DescriptionTh),
					AssetGroupDescEn: util.Ptr(asset.AssetGroup.DescriptionEn),
					AssetTypeId:      asset.AssetTypeId,
				})
			}
		}
		for _, customerGroup := range customerGroupMap[entity.BaseEntity.Id] {
			if customerGroup.CustomerGroup != nil {

				dtoResult.ServiceCustomerGroup = append(dtoResult.ServiceCustomerGroup, &dto.ServiceCustomerGroup{
					CustomerGroupId:   customerGroup.CustomerGroupId,
					CustomerGroupDesc: customerGroup.CustomerGroup.DescriptionTh,
				})
			}
		}
		for _, provider := range providerMap[entity.BaseEntity.Id] {

			dtoResult.ServiceProvider = append(dtoResult.ServiceProvider, &dto.ServiceProvider{
				ProviderName: provider.ProviderName,
				ProviderLink: provider.ProviderLink,
			})

		}
		for _, display := range displayMap[entity.BaseEntity.Id] {

			dtoResult.ServiceDisplay = append(dtoResult.ServiceDisplay, &dto.ServiceDisplay{
				DisplayId:     display.DisplayId,
				DisplayDescTh: display.DisplayDescTh,
				DisplayDescEn: display.DisplayDescEn,
			})

		}
		dtoList = append(dtoList, dtoResult)

	}

	//NOTE - Response
	resp = util.Val(util.MapPaginationResult(dtoList, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit))

	return resp, nil
}

func (s *additionalServiceService) GetAdditionalServiceByID(id int) (dto.AdditionalServiceDto, error) {
	dtoResult := dto.AdditionalServiceDto{}
	result, err := s.Repo.FindAdditionalServiceByID(id)
	if err != nil {
		if errs.IsGormNotFound(err) {
			return dto.AdditionalServiceDto{}, errs.NewError(http.StatusNotFound, err)
		}
		return dto.AdditionalServiceDto{}, errs.NewError(http.StatusInternalServerError, err)
	}

	assetMap, err := s.ServiceAssetRepo.GetAssetByAdditionalServiceIds(id)
	if err != nil {
		return dtoResult, errs.NewError(http.StatusInternalServerError, err)
	}

	customerGroupMap, err := s.ServiceCustomerGroup.GetCustomerGroupByAdditionalServiceIds(id)
	if err != nil {
		return dtoResult, errs.NewError(http.StatusInternalServerError, err)
	}

	providerMap, err := s.ServiceProviderRepo.GetProviderByAdditionalServiceIds(id)
	if err != nil {
		return dtoResult, errs.NewError(http.StatusInternalServerError, err)
	}

	displayMap, err := s.ServiceDisplayRepo.GetDisplayByAdditionalServiceIds(id)
	if err != nil {
		return dtoResult, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	dtoResult = util.MapToWithCreatedByAndUpdatedBy[dto.AdditionalServiceDto](result)
	for _, asset := range assetMap[result.BaseEntity.Id] {
		if asset.AssetType != nil && asset.AssetGroup == nil {

			dtoResult.ServiceAssetType = append(dtoResult.ServiceAssetType, &dto.ServiceAssetType{
				AssetTypeId:     asset.AssetTypeId,
				AssetTypeDescTh: util.Ptr(asset.AssetType.DescriptionTh),
				AssetTypeDescEn: util.Ptr(asset.AssetType.DescriptionEn),
			})
			dtoResult.ServiceAssetTypeIds = append(dtoResult.ServiceAssetTypeIds, asset.AssetTypeId)
		} else if asset.AssetGroup != nil {
			dtoResult.ServiceAssetGroup = append(dtoResult.ServiceAssetGroup, &dto.ServiceAssetGroup{
				AssetGroupId:     asset.AssetGroupId,
				AssetGroupDescTh: util.Ptr(asset.AssetGroup.DescriptionTh),
				AssetGroupDescEn: util.Ptr(asset.AssetGroup.DescriptionEn),
				AssetTypeId:      asset.AssetTypeId,
			})
			dtoResult.ServiceAssetGroupIds = append(dtoResult.ServiceAssetGroupIds, asset.AssetGroupId)
		}
	}
	for _, customerGroup := range customerGroupMap[result.BaseEntity.Id] {
		if customerGroup.CustomerGroup != nil {

			dtoResult.ServiceCustomerGroup = append(dtoResult.ServiceCustomerGroup, &dto.ServiceCustomerGroup{
				CustomerGroupId:   customerGroup.CustomerGroupId,
				CustomerGroupDesc: customerGroup.CustomerGroup.DescriptionTh,
			})
			dtoResult.ServiceCustomerGroupIds = append(dtoResult.ServiceCustomerGroupIds, customerGroup.CustomerGroupId)
		}
	}
	for _, provider := range providerMap[result.BaseEntity.Id] {

		dtoResult.ServiceProvider = append(dtoResult.ServiceProvider, &dto.ServiceProvider{
			ProviderName: provider.ProviderName,
			ProviderLink: provider.ProviderLink,
		})

	}
	for _, display := range displayMap[result.BaseEntity.Id] {

		dtoResult.ServiceDisplay = append(dtoResult.ServiceDisplay, &dto.ServiceDisplay{
			DisplayId:     display.DisplayId,
			DisplayDescTh: display.DisplayDescTh,
			DisplayDescEn: display.DisplayDescEn,
		})
		dtoResult.ServiceDisplayIds = append(dtoResult.ServiceDisplayIds, display.DisplayId)
	}

	return dtoResult, nil
}

func validateAdditionalService(additionalService dto.AdditionalServiceDto) error {
	if len(additionalService.ServiceAssetType) == 0 {
		return errs.NewError(http.StatusBadRequest, fmt.Errorf("asset type is required"))
	}
	if len(additionalService.ServiceAssetGroup) == 0 {
		return errs.NewError(http.StatusBadRequest, fmt.Errorf("asset group is required"))
	}
	if len(additionalService.ServiceCustomerGroup) == 0 {
		return errs.NewError(http.StatusBadRequest, fmt.Errorf("customer group is required"))
	}
	if len(additionalService.ServiceDisplay) == 0 {
		return errs.NewError(http.StatusBadRequest, fmt.Errorf("display is required"))
	}
	if additionalService.ServiceTypeId == nil || util.Val(additionalService.ServiceTypeId) <= 0 {
		return errs.NewError(http.StatusBadRequest, fmt.Errorf("service type is required"))
	}
	if additionalService.ServiceName == nil || util.Val(additionalService.ServiceName) == "" {
		return errs.NewError(http.StatusBadRequest, fmt.Errorf("service name is required"))
	}
	if additionalService.StartDate == nil {
		return errs.NewError(http.StatusBadRequest, fmt.Errorf("start date is required"))
	}

	for _, provider := range additionalService.ServiceProvider {
		if provider.ProviderName == nil || util.Val(provider.ProviderName) == "" {
			return errs.NewError(http.StatusBadRequest, fmt.Errorf("provider name is required"))
		}
		if provider.ProviderLink == nil || util.Val(provider.ProviderLink) == "" {
			return errs.NewError(http.StatusBadRequest, fmt.Errorf("provider link is required"))
		}
	}

	//NOTE - Check if each asset type has at least one associated asset group
	matched := make(map[int]bool, len(additionalService.ServiceAssetGroup))
	for _, group := range additionalService.ServiceAssetGroup {
		matched[util.Val(group.AssetTypeId)] = true
	}

	for _, assetType := range additionalService.ServiceAssetType {
		if !matched[util.Val(assetType.AssetTypeId)] {
			return errs.NewError(http.StatusBadRequest, fmt.Errorf("each selected asset type must have at least one associated asset group"))
		}
	}
	return nil
}

func (s *additionalServiceService) CreateAdditionalService(req dto.AdditionalServiceDto, actionBy *int) error {
	now := util.Now()

	//NOTE - Check Required fields
	err := validateAdditionalService(req)
	if err != nil {
		return err
	}

	//NOTE - Check for duplicate additional service
	exist, err := s.Repo.FindDuplicateRecord(util.Ptr(req), req.Id)
	if err == nil && exist != nil {
		return errs.NewBusinessError(http.StatusBadRequest, constant.Invalidate, "remark is duplicated", "error.additionalService.duplicateRemark")
	} else if err != nil && !errs.IsGormNotFound(err) {
		return errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Create Payment Due Notification
	entityAdditionalService := util.MapToPtr[entity.AdditionalService](req)
	if entityAdditionalService == nil {
		return errs.NewBusinessError(http.StatusBadRequest, constant.BadRequest, "Invalid request data", "")
	}

	entityAdditionalService.BaseEntity = &model.BaseEntity{
		CreatedBy:   actionBy,
		CreatedDate: now,
		UpdatedBy:   actionBy,
		UpdatedDate: &now,
	}

	errTx := util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {

		err := s.Repo.InsertAdditionalService(tx, entityAdditionalService)
		if err != nil {
			return errs.NewError(http.StatusInternalServerError, err)
		}

		//NOTE - Insert Asset Type and Asset Group
		asset := make([]entity.AdditionalServiceAsset, 0, len(req.ServiceAssetType)+len(req.ServiceAssetGroup))

		for _, assetType := range req.ServiceAssetType {
			assetTypeEntity := util.MapToPtr[entity.AdditionalServiceAsset](assetType)
			assetTypeEntity.AdditionalServiceId = util.Ptr(entityAdditionalService.Id)
			asset = append(asset, util.Val(assetTypeEntity))
		}
		for _, assetGroup := range req.ServiceAssetGroup {
			assetGroupEntity := util.MapToPtr[entity.AdditionalServiceAsset](assetGroup)
			assetGroupEntity.AdditionalServiceId = util.Ptr(entityAdditionalService.Id)
			asset = append(asset, util.Val(assetGroupEntity))
		}
		if len(asset) > 0 {
			if err := s.ServiceAssetRepo.BulkInsertAdditionalServiceAsset(tx, asset); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}

		//NOTE - Insert Customer Group
		customerGroups := make([]entity.AdditionalServiceCustomerGroup, 0, len(req.ServiceCustomerGroup))
		for _, customerGroup := range req.ServiceCustomerGroup {
			customerGroupEntity := util.MapToPtr[entity.AdditionalServiceCustomerGroup](customerGroup)
			customerGroupEntity.AdditionalServiceId = util.Ptr(entityAdditionalService.Id)
			customerGroups = append(customerGroups, util.Val(customerGroupEntity))
		}
		if len(customerGroups) > 0 {
			if err := s.ServiceCustomerGroup.BulkInsertAdditionalServiceCustomerGroup(tx, customerGroups); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}

		//NOTE - Insert Provider
		providers := make([]entity.AdditionalServiceProvider, 0, len(req.ServiceProvider))
		for _, provider := range req.ServiceProvider {
			providerEntity := util.MapToPtr[entity.AdditionalServiceProvider](provider)
			providerEntity.AdditionalServiceId = util.Ptr(entityAdditionalService.Id)
			providers = append(providers, util.Val(providerEntity))
		}
		if len(providers) > 0 {
			if err := s.ServiceProviderRepo.BulkInsertAdditionalServiceProvider(tx, providers); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}

		//NOTE - Insert Display
		displays := make([]entity.AdditionalServiceDisplay, 0, len(req.ServiceDisplay))
		for _, display := range req.ServiceDisplay {
			displayEntity := util.MapToPtr[entity.AdditionalServiceDisplay](display)
			displayEntity.AdditionalServiceId = util.Ptr(entityAdditionalService.Id)
			displays = append(displays, util.Val(displayEntity))
		}
		if len(displays) > 0 {
			if err := s.ServiceDisplayRepo.BulkInsertAdditionalServiceDisplay(tx, displays); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}

		return nil

	})
	if errTx != nil {
		return errTx
	}

	return nil
}

func (s *additionalServiceService) UpdateAdditionalService(req dto.AdditionalServiceDto, actionBy *int) error {
	now := util.Now()

	//NOTE - Check Required fields
	if err := validateAdditionalService(req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	//NOTE - Check for duplicate additional service
	exist, err := s.Repo.FindDuplicateRecord(util.Ptr(req), req.Id)
	if err == nil && exist != nil {
		return errs.NewBusinessError(http.StatusBadRequest, constant.Invalidate, "remark is duplicated", "error.additionalService.duplicateRemark")
	} else if err != nil && !errs.IsGormNotFound(err) {
		return errs.NewError(http.StatusInternalServerError, err)
	}

	fieldsToUpdate := map[string]interface{}{
		"service_type_id": req.ServiceTypeId,
		"service_name":    req.ServiceName,
		"detail":          req.Detail,
		"start_date":      req.StartDate,
		"end_date":        req.EndDate,
		"is_active":       req.IsActive,
		"updated_by":      actionBy,
		"updated_date":    &now,
	}

	errTx := util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		affectedRows, err := s.Repo.UpdateAdditionalService(tx, req.Id, fieldsToUpdate)
		if err != nil {
			return errs.NewError(http.StatusInternalServerError, err)
		}
		if affectedRows == 0 {
			return errs.NewBusinessError(http.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
		}

		//NOTE - Permanently delete existing service asset
		err = s.ServiceAssetRepo.PermanentDeleteAdditionalServiceAssetByServiceId(tx, req.Id)
		if err != nil {
			return errs.NewError(http.StatusInternalServerError, err)
		}

		//NOTE - Permanently delete existing service customer group
		err = s.ServiceCustomerGroup.PermanentDeleteAdditionalServiceCustomerGroupByServiceId(tx, req.Id)
		if err != nil {
			return errs.NewError(http.StatusInternalServerError, err)
		}

		//NOTE - Permanently delete existing service provider
		err = s.ServiceProviderRepo.PermanentDeleteAdditionalServiceProviderByServiceId(tx, req.Id)
		if err != nil {
			return errs.NewError(http.StatusInternalServerError, err)
		}

		//NOTE - Permanently delete existing service display
		err = s.ServiceDisplayRepo.PermanentDeleteAdditionalServiceDisplayByServiceId(tx, req.Id)
		if err != nil {
			return errs.NewError(http.StatusInternalServerError, err)
		}

		//NOTE - Insert Asset Type and Asset Group
		asset := make([]entity.AdditionalServiceAsset, 0, len(req.ServiceAssetType)+len(req.ServiceAssetGroup))

		for _, assetType := range req.ServiceAssetType {
			assetTypeEntity := util.MapToPtr[entity.AdditionalServiceAsset](assetType)
			assetTypeEntity.AdditionalServiceId = util.Ptr(req.Id)
			asset = append(asset, util.Val(assetTypeEntity))
		}
		for _, assetGroup := range req.ServiceAssetGroup {
			assetGroupEntity := util.MapToPtr[entity.AdditionalServiceAsset](assetGroup)
			assetGroupEntity.AdditionalServiceId = util.Ptr(req.Id)
			asset = append(asset, util.Val(assetGroupEntity))
		}
		if len(asset) > 0 {
			if err := s.ServiceAssetRepo.BulkInsertAdditionalServiceAsset(tx, asset); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}

		//NOTE - Insert Customer Group
		customerGroups := make([]entity.AdditionalServiceCustomerGroup, 0, len(req.ServiceCustomerGroup))
		for _, customerGroup := range req.ServiceCustomerGroup {
			customerGroupEntity := util.MapToPtr[entity.AdditionalServiceCustomerGroup](customerGroup)
			customerGroupEntity.AdditionalServiceId = util.Ptr(req.Id)
			customerGroups = append(customerGroups, util.Val(customerGroupEntity))
		}
		if len(customerGroups) > 0 {
			if err := s.ServiceCustomerGroup.BulkInsertAdditionalServiceCustomerGroup(tx, customerGroups); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}

		//NOTE - Insert Provider
		providers := make([]entity.AdditionalServiceProvider, 0, len(req.ServiceProvider))
		for _, provider := range req.ServiceProvider {
			providerEntity := util.MapToPtr[entity.AdditionalServiceProvider](provider)
			providerEntity.AdditionalServiceId = util.Ptr(req.Id)
			providers = append(providers, util.Val(providerEntity))
		}
		if len(providers) > 0 {
			if err := s.ServiceProviderRepo.BulkInsertAdditionalServiceProvider(tx, providers); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}

		//NOTE - Insert Display
		displays := make([]entity.AdditionalServiceDisplay, 0, len(req.ServiceDisplay))
		for _, display := range req.ServiceDisplay {
			displayEntity := util.MapToPtr[entity.AdditionalServiceDisplay](display)
			displayEntity.AdditionalServiceId = util.Ptr(req.Id)
			displays = append(displays, util.Val(displayEntity))
		}
		if len(displays) > 0 {
			if err := s.ServiceDisplayRepo.BulkInsertAdditionalServiceDisplay(tx, displays); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}

		return nil
	})
	if errTx != nil {
		return errTx
	}
	return nil
}

func (s *additionalServiceService) UpdateAdditionalServiceStatus(req dto.AdditionalServiceDto, actionBy *int) error {
	fieldsToUpdate := map[string]interface{}{
		"is_active":    req.IsActive,
		"updated_by":   actionBy,
		"updated_date": util.Now(),
	}

	errTx := util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		affectedRows, err := s.Repo.UpdateAdditionalService(tx, req.Id, fieldsToUpdate)
		if err != nil {
			return errs.NewError(http.StatusInternalServerError, err)
		}
		if affectedRows == 0 {
			return errs.NewBusinessError(http.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
		}

		return nil
	})
	if errTx != nil {
		return errTx
	}

	return nil
}

func (s *additionalServiceService) DeleteAdditionalService(id int, actionBy *int) error {
	result, err := s.Repo.FindAdditionalServiceByID(id)
	if err != nil {
		if errs.IsGormNotFound(err) {
			return errs.NewError(http.StatusNotFound, err)
		}
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if result.IsActive {
		return errs.NewBusinessError(fiber.StatusBadRequest, constant.Invalidate, "can not delete due to record is active", "error.additionalService.deleteActiveRecord")
	}

	errTx := util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {

		//NOTE - Delete existing asset
		err := s.ServiceAssetRepo.DeleteAdditionalServiceAssetByServiceId(tx, id)
		if err != nil {
			return errs.NewError(http.StatusInternalServerError, err)
		}

		//NOTE - Delete existing customer group
		err = s.ServiceCustomerGroup.DeleteAdditionalServiceCustomerGroupByServiceId(tx, id)
		if err != nil {
			return errs.NewError(http.StatusInternalServerError, err)
		}

		//NOTE - Delete existing provider
		err = s.ServiceProviderRepo.DeleteAdditionalServiceProviderByServiceId(tx, id)
		if err != nil {
			return errs.NewError(http.StatusInternalServerError, err)
		}

		//NOTE - Delete existing display
		err = s.ServiceDisplayRepo.DeleteAdditionalServiceDisplayByServiceId(tx, id)
		if err != nil {
			return errs.NewError(http.StatusInternalServerError, err)
		}

		//NOTE - Delete Payment Due Notification
		rowsAffected, err := s.Repo.DeleteAdditionalService(tx, id, actionBy)
		if err != nil {
			return errs.NewError(http.StatusInternalServerError, err)
		}
		if rowsAffected == 0 {
			return errs.NewError(http.StatusNotFound, err)
		}
		return nil

	})
	if errTx != nil {
		return errTx
	}

	return nil
}

func (s *additionalServiceService) GetAdditionalServiceInterest(req model.PagingRequest) (model.PagingModel[dto.AdditionalServiceInterestDto], error) {
	resp := model.PagingModel[dto.AdditionalServiceInterestDto]{}

	//NOTE - Get List
	result, err := s.ServiceInterestRepo.FindAllAdditionalServiceInterest(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Count
	count, err := s.ServiceInterestRepo.CountAllAdditionalServiceInterest()
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	var ids []int
	for _, n := range result {
		ids = append(ids, util.Val(n.AdditionalServiceId))
	}

	assetMap, err := s.ServiceAssetRepo.GetAssetByAdditionalServiceIds(ids...)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	dtoList := make([]dto.AdditionalServiceInterestDto, 0, len(result))
	for _, entity := range result {
		dtoResult := dto.AdditionalServiceInterestDto{}
		dtoResult = util.MapToWithCreatedByAndUpdatedBy[dto.AdditionalServiceInterestDto](entity)
		dtoResult.BidderId = entity.BidderId
		dtoResult.BuyerName = entity.BuyerName
		dtoResult.PhoneNumber = entity.PhoneNumber
		dtoResult.Email = entity.Email
		dtoResult.ServiceName = entity.ServiceName

		for _, asset := range assetMap[util.Val(entity.AdditionalServiceId)] {
			if asset.AssetType != nil && asset.AssetGroup == nil {

				dtoResult.ServiceAssetType = append(dtoResult.ServiceAssetType, &dto.ServiceAssetType{
					AssetTypeId:     asset.AssetTypeId,
					AssetTypeDescTh: util.Ptr(asset.AssetType.DescriptionTh),
					AssetTypeDescEn: util.Ptr(asset.AssetType.DescriptionEn),
				})
			}
		}

		dtoList = append(dtoList, dtoResult)
	}

	//NOTE - Response
	resp = util.Val(util.MapPaginationResult(dtoList, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit))

	return resp, nil
}
