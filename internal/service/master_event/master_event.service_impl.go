package service

import (
	"backend-common-lib/constant"
	"backend-common-lib/erp"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/global"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	repository "content-service/internal/repository/master_event"
	"fmt"
	"net/http"

	"time"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func (s *masterEventService) GetMasterEventAll() (dto.MasterEventListDto, error) {
	resp := dto.MasterEventListDto{}
	events, err := s.Repo.FindMasterEventAll()
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.MasterEventDto, len(events))
	for i, v := range events {
		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.MasterEventDto](v)
	}

	//NOTE - Response
	resp = dto.MasterEventListDto{
		MasterEventList: mapResult,
	}
	return resp, nil
}

func (s *masterEventService) SearchMasterEventFilter(req dto.MasterEventPageReqDto) (dto.MasterEventPageRespDto[dto.MasterEventDto], error) {
	resp := dto.MasterEventPageRespDto[dto.MasterEventDto]{}
	result, err := s.Repo.FindMasterEventWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Count
	count, err := s.Repo.CountMasterEventWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.MasterEventDto, len(result))
	for i, v := range result {
		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.MasterEventDto](v)
	}

	//NOTE Get latest sync date
	var latestSyncDate *time.Time
	if len(result) > 0 {
		latestSyncDate = result[0].LatestSyncDate
	} else {
		latestSyncDate, err = s.Repo.FindMasterEventLatestSyncDate()
		if err != nil {
			return resp, errs.NewError(http.StatusInternalServerError, err)
		}
	}

	//NOTE - Response
	resp = dto.MasterEventPageRespDto[dto.MasterEventDto]{
		PagingModel:    *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
		LatestSyncDate: latestSyncDate,
	}

	return resp, nil
}

func (s *masterEventService) UpdateMasterEventStatus(req dto.MasterEventUpdateReqDto) error {
	fieldsToUpdate := map[string]interface{}{
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": util.Now(),
	}

	affectedRows, err := s.Repo.UpdatesMasterEventFieldsWhere(fieldsToUpdate, "id = ?", req.Id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(fiber.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}

	return nil
}

func (s *masterEventService) SyncMasterEventFromErp(actionBy *int, erpConfig global.ErpConfig, authConfig global.AuthConfig) error {
	erpMap, allKeys, err := s.getEventFromErp(erpConfig, authConfig)
	if err != nil {
		return err
	}
	dbMap, err := s.getEventFromDb(allKeys)
	if err != nil {
		return err
	}
	err = s.syncEvent(actionBy, erpMap, dbMap, allKeys)
	if err != nil {
		return err
	}

	return nil
}

func (s *masterEventService) getEventFromErp(erpConfig global.ErpConfig, authConfig global.AuthConfig) (map[string]dto.MasterEventDto, map[string]struct{}, error) {
	erpList, err := erp.FetchListFromErp[dto.MasterEventSyncErpRespDto](
		erpConfig.EventUrl,
		authConfig.LoginUrl,
		authConfig.Email,
		authConfig.Password,
		map[string]interface{}{"Company_Name": "AUCT"},
		"data",
	)
	if err != nil {
		return nil, nil, errs.NewError(http.StatusInternalServerError, err)
	}

	erpMap := make(map[string]dto.MasterEventDto)
	allKeys := make(map[string]struct{})

	for _, e := range erpList {
		erpMap[util.Val(e.EventCode)] = dto.MasterEventDto{
			CompanyCode:   e.CompanyCode,
			EventCode:     e.EventCode,
			DescriptionTh: e.DescriptionTh,
			DescriptionEn: e.DescriptionEn,
			IsActive:      e.Status == "Active",
		}
		allKeys[util.Val(e.EventCode)] = struct{}{}
	}
	return erpMap, allKeys, nil
}

func (s *masterEventService) getEventFromDb(allKeys map[string]struct{}) (map[string]entity.MasterEvent, error) {
	dbList, err := s.Repo.FindMasterEventAll()
	if err != nil {
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	dbMap := make(map[string]entity.MasterEvent)
	for _, e := range dbList {
		dbMap[util.Val(e.EventCode)] = e
		allKeys[util.Val(e.EventCode)] = struct{}{}
	}
	return dbMap, nil
}

func (s *masterEventService) syncEvent(actionBy *int, erpMap map[string]dto.MasterEventDto, dbMap map[string]entity.MasterEvent, allKeys map[string]struct{}) error {
	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		repo := repository.NewMasterEventRepository(tx)
		var toInsert []entity.MasterEvent
		currentDateTime := util.NowPtr()

		for code := range allKeys {
			erp, erpExists := erpMap[code]
			db, dbExists := dbMap[code]

			if erpExists && dbExists {
				// มีทั้ง ERP + DB → compare → update หมด
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.CompanyCode = erp.CompanyCode
				temp.EventCode = erp.EventCode
				temp.DescriptionTh = erp.DescriptionTh
				temp.DescriptionEn = erp.DescriptionEn
				temp.IsActive = erp.IsActive
				temp.IsDeletedByErp = false
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterEventAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}

			} else if erpExists && !dbExists {
				// มีใน ERP แต่ไม่มีใน DB → insert
				temp := entity.MasterEvent{
					BaseEntity: &model.BaseEntity{
						CreatedDate: *currentDateTime,
						CreatedBy:   actionBy,
						UpdatedDate: currentDateTime,
						UpdatedBy:   actionBy,
					},
					CompanyCode:    erp.CompanyCode,
					EventCode:      erp.EventCode,
					DescriptionTh:  erp.DescriptionTh,
					DescriptionEn:  erp.DescriptionEn,
					IsActive:       erp.IsActive,
					IsDeletedByErp: false,
					LatestSyncDate: currentDateTime,
				}
				toInsert = append(toInsert, temp)

			} else if !erpExists && dbExists {
				// มีใน DB แต่ไม่มีใน ERP → mark delete
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.IsActive = false
				temp.IsDeletedByErp = true
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterEventAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}
			}
		}

		if len(toInsert) > 0 {
			if err := repo.InsertMasterEventList(toInsert); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}
		return nil
	})
}
