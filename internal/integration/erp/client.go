package erp

// func FetchListFromErp[Resp any](
// 	url string,
// 	token string,
// 	body map[string]interface{},
// 	field string,
// ) ([]Resp, error) {
// 	header := map[string]string{"Authorization": "Bearer " + token}

// 	resp, err := services.NewHttpService().DoRequest(services.HttpGET, url, header, body)
// 	if err != nil {
// 		return nil, errs.NewError(http.StatusInternalServerError, err)
// 	}

// 	parsed, err := util.ParseFieldFromJSON[[]Resp](resp, field)
// 	if err != nil {
// 		return nil, errs.NewError(http.StatusInternalServerError, err)
// 	}

// 	return parsed, nil
// }

// func FetchListFromErpWithLongTimeout[Resp any](
// 	url string,
// 	token string,
// 	body map[string]interface{},
// 	field string,
// ) ([]Resp, error) {
// 	header := map[string]string{"Authorization": "Bearer " + token}

// 	resp, err := services.NewHttpService().DoRequestWithLongTimeout(services.HttpGET, url, header, body)
// 	if err != nil {
// 		return nil, errs.NewError(http.StatusInternalServerError, err)
// 	}

// 	parsed, err := util.ParseFieldFromJSON[[]Resp](resp, field)
// 	if err != nil {
// 		return nil, errs.NewError(http.StatusInternalServerError, err)
// 	}

// 	return parsed, nil
// }
