package router

import (
	"backend-common-lib/integrates/cache"
	additionalService "content-service/internal/api/additional_service"
	buyerEditProfileRequest "content-service/internal/api/buyer_edit_profile_request"
	buyerRegistrationRequest "content-service/internal/api/buyer_registration_request"
	dropdown "content-service/internal/api/dropdown"
	feature "content-service/internal/api/feature"
	floorSpecialRemark "content-service/internal/api/floor_special_remark"
	masterAssetGroup "content-service/internal/api/master_asset_group"
	masterAssetLocationFloor "content-service/internal/api/master_asset_location_floor"
	masterAssetType "content-service/internal/api/master_asset_type"
	masterBank "content-service/internal/api/master_bank"
	masterBranch "content-service/internal/api/master_branch"
	masterCity "content-service/internal/api/master_city"
	masterCostRevenue "content-service/internal/api/master_cost_revenue"
	masterCountry "content-service/internal/api/master_country"
	masterCustomer "content-service/internal/api/master_customer"
	masterCustomerGroup "content-service/internal/api/master_customer_group"
	masterCustomerSellerOffer "content-service/internal/api/master_customer_seller_offer"
	masterCustomerType "content-service/internal/api/master_customer_type"
	masterDepartment "content-service/internal/api/master_department"
	masterDistrict "content-service/internal/api/master_district"
	masterEvent "content-service/internal/api/master_event"
	masterHoliday "content-service/internal/api/master_holiday"
	masterPaymentMethod "content-service/internal/api/master_payment_method"
	masterPostcode "content-service/internal/api/master_postcode"
	masterPrefixName "content-service/internal/api/master_prefix_name"
	masterReason "content-service/internal/api/master_reason"
	masterRegion "content-service/internal/api/master_region"
	masterRegisterType "content-service/internal/api/master_register_type"
	masterRegisterTypeCar "content-service/internal/api/master_register_type_car"
	masterSaleChannel "content-service/internal/api/master_sale_channel"
	masterSellerOffer "content-service/internal/api/master_seller_offer"
	masterSubDistrict "content-service/internal/api/master_sub_district"
	masterVatBusiness "content-service/internal/api/master_vat_business"
	masterVatCode "content-service/internal/api/master_vat_code"
	masterVendor "content-service/internal/api/master_vendor"
	masterVendorGroup "content-service/internal/api/master_vendor_group"
	"content-service/internal/api/member"
	passwordExpiry "content-service/internal/api/password_expiry"
	paymentDueNotification "content-service/internal/api/payment_due_notification"
	policyConsent "content-service/internal/api/policy_consent"
	productPickupDueNotification "content-service/internal/api/product_pickup_due_notification"
	proxyBidCancelReason "content-service/internal/api/proxy_bid_cancel_reason"
	registrationBookDueNotification "content-service/internal/api/registration_book_due_notification"
	reprintSlipReason "content-service/internal/api/reprint_slip_reason"
	userBidderNumbers "content-service/internal/api/user_bidder_numbers"
	"content-service/internal/global"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func RegisterRoutes(api fiber.Router, db *gorm.DB, config *global.Config, rdb cache.RedisContext) {

	masterGroup := api.Group("master")
	masterPaymentMethod.Register(masterGroup, db, config)
	masterAssetType.Register(masterGroup, db, config)
	masterAssetGroup.Register(masterGroup, db, config)
	masterEvent.Register(masterGroup, db, config)
	masterSaleChannel.Register(masterGroup, db, config)
	masterBranch.Register(masterGroup, db, config)
	masterBank.Register(masterGroup, db, config)
	masterVendor.Register(masterGroup, db, config)
	masterVendorGroup.Register(masterGroup, db, config)
	masterHoliday.Register(masterGroup, db, config)
	masterPostcode.Register(masterGroup, db, config)
	masterAssetLocationFloor.Register(masterGroup, db, config)
	masterCustomerGroup.Register(masterGroup, db, config)
	masterCustomerType.Register(masterGroup, db, config)
	masterCostRevenue.Register(masterGroup, db, config)
	masterDepartment.Register(masterGroup, db, config)
	masterCustomer.Register(masterGroup, db, config)
	masterDistrict.Register(masterGroup, db, config)
	masterCountry.Register(masterGroup, db, config)
	masterPrefixName.Register(masterGroup, db, config)
	masterVatCode.Register(masterGroup, db, config)
	masterReason.Register(masterGroup, db, config)
	masterRegion.Register(masterGroup, db, config)
	masterVatBusiness.Register(masterGroup, db, config)
	masterSubDistrict.Register(masterGroup, db, config)
	masterCity.Register(masterGroup, db, config)
	masterRegisterType.Register(masterGroup, db, config)
	masterRegisterTypeCar.Register(masterGroup, db, config)
	masterCustomerSellerOffer.Register(masterGroup, db, config)
	masterSellerOffer.Register(masterGroup, db, config)

	// sample_test.Register(masterGroup, rdb)
	auctionSettingGroup := api.Group("auction-setting")
	paymentDueNotification.Register(auctionSettingGroup, db, config)
	registrationBookDueNotification.Register(auctionSettingGroup, db, config)
	productPickupDueNotification.Register(auctionSettingGroup, db, config)
	reprintSlipReason.Register(auctionSettingGroup, db, config)
	floorSpecialRemark.Register(auctionSettingGroup, db, config)
	additionalService.Register(auctionSettingGroup, db, config)

	proxyBidGroup := api.Group("proxy-bid")
	proxyBidCancelReason.Register(proxyBidGroup, db, config)

	buyerSettingGroup := api.Group("buyer-setting")
	policyConsent.Register(buyerSettingGroup, db, config)

	orgSettingGroup := api.Group("org-setting")
	passwordExpiry.Register(orgSettingGroup, db, config)

	userBidderNumberGroup := api.Group("user-bidder-number")
	userBidderNumbers.Register(userBidderNumberGroup, db, config)

	feature.Register(api, db, config)
	groupMember := api.Group("member")
	member.Register(groupMember, db)
	buyerRegistrationRequest.Register(groupMember, db)
	buyerEditProfileRequest.Register(groupMember, db)

}

func RegisterDropdownRoutes(api fiber.Router, db *gorm.DB, config *global.Config, rdb cache.RedisContext) {
	dropdown.Register(api, db, config)
}
