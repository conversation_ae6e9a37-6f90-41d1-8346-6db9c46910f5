package masterprefixname

import (
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/global"
	"content-service/internal/model/dto"
	service "content-service/internal/service/master_prefix_name"
	"net/http"
	"strconv"

	"github.com/gofiber/fiber/v2"
)

type Handler struct {
	Service    service.MasterPrefixNameService
	ErpConfig  global.ErpConfig
	AuthConfig global.AuthConfig
}

func (h *Handler) SearchMasterPrefixNameFilter(c *fiber.Ctx) error {
	var req dto.MasterPrefixNamePageReqDto

	if err := c.BodyParser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	res, err := h.Service.SearchMasterPrefixNameFilter(req)
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) UpdateMasterPrefixNameStatus(c *fiber.Ctx) error {
	var req dto.MasterPrefixNameUpdateReqDto

	if err := c.BodyParser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	idStr := c.Params("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}
	req.Id = id

	actionBy := util.GetActionByFromHeader(c)
	req.ActionBy = actionBy

	err = h.Service.UpdateMasterPrefixNameStatus(req)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))
}

func (h *Handler) SyncMasterPrefixNameFromErp(c *fiber.Ctx) error {
	var req model.BaseDtoActionBy

	actionBy := util.GetActionByFromHeader(c)
	req.ActionBy = actionBy

	err := h.Service.SyncMasterPrefixNameFromErp(req.ActionBy, h.ErpConfig, h.AuthConfig)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))
}
