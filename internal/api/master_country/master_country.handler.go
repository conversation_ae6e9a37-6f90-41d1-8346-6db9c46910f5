package mastercountry

import (
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/global"
	"content-service/internal/model/dto"
	service "content-service/internal/service/master_country"
	"net/http"
	"strconv"

	"github.com/gofiber/fiber/v2"
)

type Handler struct {
	Service    service.MasterCountryService
	ErpConfig  global.ErpConfig
	AuthConfig global.AuthConfig
}

func (h *Handler) SearchMasterCountryFilter(c *fiber.Ctx) error {
	var req dto.MasterCountryPageReqDto

	if err := c.BodyParser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	res, err := h.Service.SearchMasterCountryFilter(req)
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))

}

func (h *Handler) UpdateMasterCountryStatus(c *fiber.Ctx) error {
	var req dto.MasterCountryUpdateReqDto

	if err := c.BodyParser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	idStr := c.Params("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}
	req.Id = id

	actionBy := util.GetActionByFromHeader(c)
	req.ActionBy = actionBy

	err = h.Service.UpdateMasterCountryStatus(req)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))

}

func (h *Handler) SyncMasterCountryFromErp(c *fiber.Ctx) error {
	var req model.BaseDtoActionBy

	actionBy := util.GetActionByFromHeader(c)
	req.ActionBy = actionBy

	err := h.Service.SyncMasterCountryFromErp(req.ActionBy, h.ErpConfig, h.AuthConfig)
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))

}
