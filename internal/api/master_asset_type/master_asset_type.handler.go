package masterassettype

import (
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/global"
	"content-service/internal/model/dto"
	service "content-service/internal/service/master_asset_type"
	"net/http"
	"strconv"

	"github.com/gofiber/fiber/v2"
)

type Handler struct {
	Service    service.MasterAssetTypeService
	ErpConfig  global.ErpConfig
	AuthConfig global.AuthConfig
}

func (h *Handler) SearchMasterAssetTypeFilter(c *fiber.Ctx) error {
	var req dto.MasterAssetTypePageReqDto

	if err := c.BodyParser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	res, err := h.Service.SearchMasterAssetTypeFilter(req)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))

}

func (h *Handler) UpdateMasterAssetTypeStatus(c *fiber.Ctx) error {
	var req dto.MasterAssetTypeUpdateReqDto

	if err := c.BodyParser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	idStr := c.Params("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}
	req.Id = id

	actionBy := util.GetActionByFromHeader(c)
	req.ActionBy = actionBy

	err = h.Service.UpdateMasterAssetTypeStatus(req)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))

}

func (h *Handler) SyncMasterAssetTypeFromErp(c *fiber.Ctx) error {
	var req model.BaseDtoActionBy

	actionBy := util.GetActionByFromHeader(c)
	req.ActionBy = actionBy

	err := h.Service.SyncMasterAssetTypeFromErp(req.ActionBy, h.ErpConfig, h.AuthConfig)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))

}

func (h *Handler) FindMasterAssetTypeAll(c *fiber.Ctx) error {
	res, err := h.Service.FindMasterAssetTypeAll()
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}
