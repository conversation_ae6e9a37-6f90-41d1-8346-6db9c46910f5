package mastercustomergroup

import (
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/global"
	"content-service/internal/model/dto"
	service "content-service/internal/service/master_customer_group"
	"net/http"

	"strconv"

	"github.com/gofiber/fiber/v2"
)

type Handler struct {
	Service    service.MasterCustomerGroupService
	ErpConfig  global.ErpConfig
	AuthConfig global.AuthConfig
}

func (h *Handler) SearchMasterCustomerGroupFilter(c *fiber.Ctx) error {
	var req dto.MasterCustomerGroupPageReqDto

	if err := c.BodyParser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	res, err := h.Service.SearchMasterCustomerGroupFilter(req)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *<PERSON><PERSON>) UpdateMasterCustomerGroupStatus(c *fiber.Ctx) error {
	var req dto.MasterCustomerGroupUpdateReqDto

	if err := c.Body<PERSON>arser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	idStr := c.Params("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}
	req.Id = id

	actionBy := util.GetActionByFromHeader(c)
	req.ActionBy = actionBy

	err = h.Service.UpdateMasterCustomerGroupStatus(req)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))
}

func (h *Handler) SyncMasterCustomerGroupFromErp(c *fiber.Ctx) error {
	var req model.BaseDtoActionBy

	actionBy := util.GetActionByFromHeader(c)
	req.ActionBy = actionBy

	err := h.Service.SyncMasterCustomerGroupFromErp(req.ActionBy, h.ErpConfig, h.AuthConfig)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))
}

func (h *Handler) FindMasterCustomerGroupAll(c *fiber.Ctx) error {
	res, err := h.Service.FindMasterCustomerGroupAll()
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}
