package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"fmt"

	"gorm.io/gorm"
)

func (r *buyerEditProfileRequestRepositoryImpl) buildBuyerEditProfileWithFilterQuery(req dto.BuyerEditProfileSearchReqDto) *gorm.DB {
	query := r.DB.Model(&entity.BuyerEditProfileRequest{}).Select(
		`buyer_edit_profile_request.*,
		COALESCE(buyer_edit_profile_request.identification_number, buyer_edit_profile_request.tax_id) AS identification_number,
		pa.description_th AS prefix_name_th,
		pa.description_en AS prefix_name_en,
		nationality.nationality_th AS nationality_th,
		nationality.nationality_en AS nationality_en`).
		Joins("LEFT JOIN (SELECT id,description_th,description_en FROM master_prefix_name) AS pa ON pa.id = buyer_edit_profile_request.prefix_name_id").
		Joins("LEFT JOIN (SELECT id,nationality_th,nationality_en FROM master_country) AS nationality ON nationality.id = buyer_edit_profile_request.nationality_id")

	if req.Search != nil {
		query = query.Where("buyer_edit_profile_request.username LIKE ?", fmt.Sprintf("%%%s%%", util.Val(req.Search)))
	}

	if req.NationalityId != nil {
		query = query.Where("buyer_edit_profile_request.nationality_id = ?", req.NationalityId)
	}

	if req.ApprovalStatus != nil {
		query = query.Where("buyer_edit_profile_request.approval_status = ?", req.ApprovalStatus)
	}

	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "buyer_edit_profile_request")
	} else {
		orderClause := fmt.Sprintf(`
				CASE
					WHEN buyer_edit_profile_request.approval_status = 'WAITING' THEN 1
					ELSE 2
				END %s`, req.SortOrder)
		query = query.Order(orderClause)
	}

	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	return query
}

func (r *buyerEditProfileRequestRepositoryImpl) FindBuyerEditProfileWithFilter(req dto.BuyerEditProfileSearchReqDto) ([]entity.BuyerEditProfileRequest, error) {
	var results []entity.BuyerEditProfileRequest
	query := r.buildBuyerEditProfileWithFilterQuery(req)
	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *buyerEditProfileRequestRepositoryImpl) CountBuyerEditProfileWithFilter(req dto.BuyerEditProfileSearchReqDto) (int64, error) {
	var count int64
	query := r.buildBuyerEditProfileWithFilterQuery(req)
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *buyerEditProfileRequestRepositoryImpl) GetById(id int) (entity.BuyerEditProfileRequest, error) {
	var buyerEditProfileRequest entity.BuyerEditProfileRequest
	err := r.DB.Table("buyer_edit_profile_request").Select(
		`buyer_edit_profile_request.*,
		pa.description_th AS prefix_name_th,
		pa.description_en AS prefix_name_en,
		sd.description_th AS sub_district_description_th,
		sd.description_en AS sub_district_description_en,
		d.description_th AS district_description_th,
		d.description_en AS district_description_en,
		p.description_th AS province_description_th,
		p.description_en AS province_description_en,
		c.description_th AS country_description_th,
		c.description_en AS country_description_en,
		nationality.nationality_th AS nationality_th,
		nationality.nationality_en AS nationality_en`).
		Joins("LEFT JOIN (SELECT id,description_th,description_en FROM master_prefix_name) AS pa ON pa.id = buyer_edit_profile_request.prefix_name_id").
		Joins("LEFT JOIN (SELECT id,description_th,description_en FROM master_sub_district) AS sd ON sd.id = buyer_edit_profile_request.sub_district_id").
		Joins("LEFT JOIN (SELECT id,description_th,description_en FROM master_district) AS d ON d.id = buyer_edit_profile_request.district_id").
		Joins("LEFT JOIN (SELECT id,description_th,description_en FROM master_city) AS p ON p.id = buyer_edit_profile_request.province_id").
		Joins("LEFT JOIN (SELECT id,description_th,description_en FROM master_country) AS c ON c.id = buyer_edit_profile_request.country_id").
		Joins("LEFT JOIN (SELECT id,nationality_th,nationality_en FROM master_country) AS nationality ON nationality.id = buyer_edit_profile_request.nationality_id").
		First(&buyerEditProfileRequest, id).Error
	return buyerEditProfileRequest, err
}

func (r *buyerEditProfileRequestRepositoryImpl) GetAllByIds(ids []int) ([]*entity.BuyerEditProfileRequest, error) {
	var buyerEditProfileRequests []*entity.BuyerEditProfileRequest
	err := r.DB.Table("buyer_edit_profile_request").Select(
		`buyer_edit_profile_request.*,
		pa.description_th AS prefix_name_th,
		pa.description_en AS prefix_name_en,
		sd.description_th AS sub_district_description_th,
		sd.description_en AS sub_district_description_en,
		d.description_th AS district_description_th,
		d.description_en AS district_description_en,
		p.description_th AS province_description_th,
		p.description_en AS province_description_en,
		c.description_th AS country_description_th,
		c.description_en AS country_description_en,
		nationality.nationality_th AS nationality_th,
		nationality.nationality_en AS nationality_en`).
		Joins("LEFT JOIN (SELECT id,description_th,description_en FROM master_prefix_name) AS pa ON pa.id = buyer_edit_profile_request.prefix_name_id").
		Joins("LEFT JOIN (SELECT id,description_th,description_en FROM master_sub_district) AS sd ON sd.id = buyer_edit_profile_request.sub_district_id").
		Joins("LEFT JOIN (SELECT id,description_th,description_en FROM master_district) AS d ON d.id = buyer_edit_profile_request.district_id").
		Joins("LEFT JOIN (SELECT id,description_th,description_en FROM master_city) AS p ON p.id = buyer_edit_profile_request.province_id").
		Joins("LEFT JOIN (SELECT id,description_th,description_en FROM master_country) AS c ON c.id = buyer_edit_profile_request.country_id").
		Joins("LEFT JOIN (SELECT id,nationality_th,nationality_en FROM master_country) AS nationality ON nationality.id = buyer_edit_profile_request.nationality_id").
		Where("buyer_edit_profile_request.id IN ?", ids).Find(&buyerEditProfileRequests).Error
	return buyerEditProfileRequests, err
}

func (r *buyerEditProfileRequestRepositoryImpl) GetAllBuyerPastByIds(ids []int) ([]*entity.BuyerEditProfileRequestPast, error) {
	var buyerEditProfileRequestPasts []*entity.BuyerEditProfileRequestPast
	err := r.DB.Table("buyer_edit_profile_request_past").Select(
		`buyer_edit_profile_request_past.*,
		current.buyer_edit_profile_request_id,
		pa.description_th AS prefix_name_th,
		pa.description_en AS prefix_name_en,
		sd.description_th AS sub_district_description_th,
		sd.description_en AS sub_district_description_en,
		d.description_th AS district_description_th,
		d.description_en AS district_description_en,
		p.description_th AS province_description_th,
		p.description_en AS province_description_en,
		c.description_th AS country_description_th,
		c.description_en AS country_description_en,
		nationality.nationality_th AS nationality_th,
		nationality.nationality_en AS nationality_en`).
		Joins("LEFT JOIN (SELECT id,description_th,description_en FROM master_prefix_name) AS pa ON pa.id = buyer_edit_profile_request_past.prefix_name_id").
		Joins("LEFT JOIN (SELECT id,description_th,description_en FROM master_sub_district) AS sd ON sd.id = buyer_edit_profile_request_past.sub_district_id").
		Joins("LEFT JOIN (SELECT id,description_th,description_en FROM master_district) AS d ON d.id = buyer_edit_profile_request_past.district_id").
		Joins("LEFT JOIN (SELECT id,description_th,description_en FROM master_city) AS p ON p.id = buyer_edit_profile_request_past.province_id").
		Joins("LEFT JOIN (SELECT id,description_th,description_en FROM master_country) AS c ON c.id = buyer_edit_profile_request_past.country_id").
		Joins("LEFT JOIN (SELECT id,nationality_th,nationality_en FROM master_country) AS nationality ON nationality.id = buyer_edit_profile_request_past.nationality_id").
		Joins("LEFT JOIN (SELECT id,buyer_edit_profile_request_id FROM buyer_edit_profile_request_current) AS current ON current.id = buyer_edit_profile_request_past.buyer_edit_profile_request_current_id").
		Where("current.buyer_edit_profile_request_id IN ?", ids).Find(&buyerEditProfileRequestPasts).Error
	return buyerEditProfileRequestPasts, err
}

func (r *buyerEditProfileRequestRepositoryImpl) UpdateFields(buyerEditProfileRequestId int, fields map[string]interface{}) error {
	err := r.DB.Model(&entity.BuyerEditProfileRequest{}).Where("id = ?", buyerEditProfileRequestId).Updates(fields).Error
	return err
}

func (r *buyerEditProfileRequestRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
