package repository

import (
	"content-service/internal/model/entity"
	"time"
)

func (r *userBidderNumbersRepositoryImpl) FindUserBidderNumberByBidderNumber(bidderNumberId string, soldDate time.Time) (*entity.UserBidderNumbers, error) {
	var userBidderNumbers *entity.UserBidderNumbers
	startOfDay := time.Date(soldDate.Year(), soldDate.Month(), soldDate.Day(), 0, 0, 0, 0, time.Local)
	endOfDay := startOfDay.Add(24*time.Hour - time.Nanosecond)

	err := r.DB.Table("user_bidder_numbers").
		Select(
			`
			user_bidder_numbers.id,
			user_bidder_numbers.user_id,
			user_bidder_numbers.bidder_number,
			b.first_name as buyer_first_name,
			b.middle_name as buyer_middle_name,
			b.last_name as buyer_last_name,
			mpn.description_th as prefix_name

			`,
		).
		Joins("LEFT JOIN buyer b ON b.user_id = user_bidder_numbers.user_id").
		Joins("LEFT JOIN master_prefix_name mpn ON mpn.id = b.prefix_name_id").
		Where("user_bidder_numbers.bidder_number = ?  AND user_bidder_numbers.created_date BETWEEN ? AND ? AND user_bidder_numbers.lot_id = NULL", bidderNumberId, startOfDay, endOfDay).First(&userBidderNumbers).Error

	return userBidderNumbers, err
}

func (r *userBidderNumbersRepositoryImpl) FindUserBidderNumberByBidderNumberAndLotId(bidderNumberId string, lotId int, soldDate time.Time) (*entity.UserBidderNumbers, error) {
	var userBidderNumbers *entity.UserBidderNumbers
	startOfDay := time.Date(soldDate.Year(), soldDate.Month(), soldDate.Day(), 0, 0, 0, 0, time.Local)
	endOfDay := startOfDay.Add(24*time.Hour - time.Nanosecond)

	err := r.DB.Table("user_bidder_numbers").
		Select(
			`
			user_bidder_numbers.id,
			user_bidder_numbers.user_id,
			user_bidder_numbers.bidder_number,
			b.first_name as buyer_first_name,
			b.middle_name as buyer_middle_name,
			b.last_name as buyer_last_name,
			mpn.description_th as prefix_name

			`,
		).
		Joins("LEFT JOIN buyer b ON b.user_id = user_bidder_numbers.user_id").
		Joins("LEFT JOIN master_prefix_name mpn ON mpn.id = b.prefix_name_id").
		Where("user_bidder_numbers.bidder_number = ? AND user_bidder_numbers.lot_id = ? AND user_bidder_numbers.created_date BETWEEN ? AND ?", bidderNumberId, lotId, startOfDay, endOfDay).First(&userBidderNumbers).Error

	return userBidderNumbers, err
}
