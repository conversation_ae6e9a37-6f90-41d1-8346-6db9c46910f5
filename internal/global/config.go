package global

type Config struct {
	App   AppConfig   `mapstructure:"app"`
	DB    DBConfig    `mapstructure:"dbConfig"`
	Erp   ErpConfig   `mapstructure:"ErpConfig"`
	Redis RedisConfig `mapstructure:"redisConfig"`
	Auth  AuthConfig  `mapstructure:"authConfig"`
}

type AppConfig struct {
	AppName      string `mapstructure:"appName"`
	HttpPort     int    `mapstructure:"httpPort"`
	Domain       string `mapstructure:"domain"`
	AllowOrigins string `mapstructure:"allowOrigins"`
	CertFile     string `mapstructure:"certFile"`
	KeyFile      string `mapstructure:"keyFile"`
}

type DBConfig struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	DBName   string `mapstructure:"dbName"`
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
}

type RedisConfig struct {
	Addr     string `mapstructure:"addr"`
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Protocol int    `mapstructure:"protocol"`
	Password string `mapstructure:"password"`
	DB       int    `mapstructure:"db"`
}

type ErpConfig struct {
	Token                  string
	EventUrl               string
	AssetGroupUrl          string
	AssetTypeUrl           string
	SaleChannelUrl         string
	DepartmentUrl          string
	VendorUrl              string
	VendorGroupUrl         string
	BranchUrl              string
	CustomerGroupUrl       string
	CustomerTypeUrl        string
	AssetLocationFloorUrl  string
	ReasonUrl              string
	LotSouUrl              string
	LotSouLotLineUrl       string
	VatCodeUrl             string
	RegionUrl              string
	CostRevenueUrl         string
	VatBusinessUrl         string
	CountryUrl             string
	DistrictUrl            string
	SubDistrictUrl         string
	PrefixNameUrl          string
	CityUrl                string
	CustomerUrl            string
	PostcodeUrl            string
	HolidayUrl             string
	BankUrl                string
	PaymentMethodUrl       string
	RegisterTypeUrl        string
	RegisterTypeCarUrl     string
	CustomerSellerOfferUrl string
	SellerOfferUrl         string
}

type AuthConfig struct {
	LoginUrl string `mapstructure:"loginUrl"`
	Email    string `mapstructure:"email"`
	Password string `mapstructure:"password"`
}
